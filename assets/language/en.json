{"message": "Message", "bank_info": "Bank Info", "balance": "Balance", "order_no": "Order", "items": "Items", "phone_no": "Contact", "bank_name": "Bank Name", "branch_name": "Branch Name", "holder_name": "A/C Holder Name", "account_no": "A/C No.", "bank_name_hint": "Ex: City Bank", "branch_name_hint": "Ex: Texas Branch", "holder_name_hint": "Ex: <PERSON>", "account_no_hint": "Ex: ****************", "business_settings": "Shipping Method", "ok": "Ok", "choose_language": "Choose Language", "settings_screen": "Settings Screen", "shipping_method": "Shipping Method", "title": "Ex: Inner City", "duration": "Duration", "cost": "Cost", "settings": "Settings", "withdraw_request": "Withdraw Request", "amount": "Amount", "close": "Close", "incomplete_bank_info": "Incomplete Bank Info", "shipping_address": "Shipping Address", "item": "<PERSON><PERSON>", "view_details": "View Details", "home": "Home", "menu": "<PERSON><PERSON>", "current_balance": "Current Balance", "payment_method": "Payment Method", "enter_amount": "Enter Amount", "withdraw": "Withdrawn", "your_withdrawal": "Your withdrawal request has been received", "delivery_address": "Delivery Address", "assign_delivery_boy": "Assign Del<PERSON><PERSON>", "customer_contact_details": "Customer Contact Details", "call": "Call", "are_you_sure": "Are You Sure?", "you_want_to_cancel": "You want to cancel this Request?", "my_shop": "My Shop", "profile": "Profile", "my_order": "My Order", "language": "Language", "logout": "Logout", "type_message_here": "Type message here ...", "choose_the_language": "Choose the language", "english": "English", "arabic": "Arabic", "save": "Save", "welcome": "Welcome", "login": "Log In", "email": "Email", "password_did_not_match": "Password does not matched", "mobile_number": "Mobile Number", "number_hint": "1234567890", "password": "Password", "password_hint": "••••••••••", "remember_me": "Remember me", "first_name": "First Name", "last_name": "Last Name", "no_data_available": "No data available", "order_id": "Order ID", "details": "Details", "my_profile": "My Profile", "send": "Send", "update_profile": "Update Profile", "order": "Order", "cancel": "Cancel", "dark_theme": "Dark Mode", "nothing_found": "Nothing found", "yes": "Yes", "no": "No", "are_you_sure_to_cancel": "Are you sure you want to cancel this order?", "want_to_sign_out": "Logout from Vendor App?", "send_a_message": "Send a Message", "no_connection": "No connection", "connected": "Connected", "change_something_to_update": "Change something to update", "updated_successfully": "Updated Successfully", "password_should_be": "Password should be equal or greater than 6 character", "enter_email_address": "Enter your email", "enter_valid_email": "Enter valid email", "enter_phone_number": "Enter phone number", "enter_first_name": "Enter name", "enter_last_name": "Enter last name", "next": "Next", "order_details": "Order Details", "enter_address": "Enter Address", "enter_bank_name": "Enter Bank Name", "enter_branch_name": "Enter Branch Name", "enter_holder_name": "Enter Holder Name", "enter_account": "Enter Account No", "item_price": "<PERSON><PERSON>", "tax": "Tax", "discount": "Discount", "total_amount": "Total", "pending": "Pending", "processing": "Packaging", "delivered": "Delivered", "return": "Return", "failed": "Failed to Delivery", "price": "Price", "name": "Name", "city": "City", "zip_code": "Zip Code", "phone": "Phone", "contact": "Contact", "cancel_order": "Cancel Order", "request": "Request", "cash_on_delivery": "Cash On Delivery", "no_data_found": "No Data Found", "sub_total": "Sub Total", "shipping_fee": "Shipping Fee", "balance_withdraw": "Withdrawable Balance", "withdrawn": "Withdrawn", "total_earning": "Total Earning", "transactions": "Transactions", "transaction_screen": "Transactions Screen", "submit": "Submit", "select_order_type": "Select Order Type", "monthly_earning": "Monthly Earning", "vendor": "<PERSON><PERSON><PERSON>", "all": "All", "inbox": "Inbox", "address": "Address", "edit": "Edit", "currency": "<PERSON><PERSON><PERSON><PERSON>", "approved": "Approved", "denied": "Denied", "enter_balance": "Enter balance", "enter_lower_amount": "Enter lower amount than your balance", "enter_minimum_amount of": "Enter minimum amount of", "more": "Setting", "orders": "Orders", "confirmed": "Confirmed", "out_for_delivery": "Out For Delivery", "cancelled": "Cancelled", "your_earnings": "Your Earnings", "commission_given": "Commission Given", "wallet": "Wallet", "withdraw_history": "Transaction History", "enter_Amount": "Enter Amount", "branch": "Branch ", "account_number": "Account Number : ", "all_products": "Products", "product_review": "Reviews", "ratting": "Rattings", "total_products": "Total\nproducts", "total_order": "Total order", "change_password": "Change Password", "edit_profile": "Edit Profile", "shipping_methods": "Shipping Methods", "no_shipping_method": "No Shipping Method Suggestion for Admin\nPlease Submit Request! ", "shop_settings": "Shop Settings", "update_logo": "Update Logo", "update_shop": "Update Shop", "shop_name": "Shop Name", "contact_number": "Contact Number", "add_product": "Add product", "earning_statistic": "Earning Statistics", "shop_info_updated_successfully": "Shop Info Updated Successfully", "out_delivery": " Out for Delivery", "attribute": "Attribute", "enter_a_variant_name": "Enter V<PERSON>t Name", "variant": "<PERSON><PERSON><PERSON>", "quantity": "Quantity", "general_info": "General info", "category": "Category", "sub_category": "Sub Category", "select": "Select", "unit_price": "Unit Price", "purchase_price": "Purchase Price", "tax_p": "Tax(%)", "discount_type": "Discount Type", "total_quantity": "Stock Quantity", "seo_section": "SEO Section (Optional)", "meta_title": "Meta Title", "meta_description": "Meta Description", "meta_description_hint": "Enter description ", "meta_image": "Meta Image", "image": "Image", "youtube_video_link": "Youtube video Link", "upload_thumbnail": "Upload thumbnail", "update_product": "Update Product", "product_name": "Product Name", "product_description": "Product Description", "sub_sub_category": "Sub sub Category", "brand": "Brand", "unit": "Unit", "colors": "Colors", "product_added_successfully": "Product added successfully", "product_updated_successfully": "Product updated successfully", "no_variant_added_yet": "No variant added yet", "enter_product_name": "Enter Product Name", "enter_product_description": "Enter product description", "search_color": "Search Color", "product_price_and_stock": "Price & Stock", "variations": "Variations", "product_title": "Product Title", "enter_unit_price": "Enter unit price", "enter_purchase_price": "Enter purchase price", "enter_product_discount": "Enter Product Discount", "select_a_category": "select a category", "add_at_least_one_variant_for_every_attribute": "Add at least one variant for every attribute", "enter_price_for_every_variant": "Enter price for every variant", "enter_quantity_for_every_variant": "Enter quantity for every variant", "upload_product_image": "Upload product image", "enter_seo_title_discount": "Enter seo title", "enter_seo_description_discount": "Enter seo description", "update": "Update", "product_deleted_successfully": "Product deleted successfully", "product_image": "Product Image", "are_you_sure_want_to_delete_this_product": "Are you sure? Want to delete this product", "please_input_all_title": "Please input all language title", "please_input_all_des": "Please input all language description", "upload_thumbnail_image": "Upload product thumbnail", "terms_and_condition": "Terms & Condition", "about_us": "About Us", "privacy_policy": "Privacy Policy", "pending_withdrawn": "Pending Withdrawn", "delivery_charge_earned": "Delivery Charge Earned", "collected_cash": "Collected Cash", "total_collected_tax": "Total Collected Tax", "commission_given_wallet": "Commission Given", "enter_title": "Enter title", "enter_cost": "Enter cost", "enter_duration": "Enter duration", "shipping_method_added_successfully": "Shipping Method added successfully", "shipping_method_update_successfully": "Shipping Method updated successfully", "shipping_method_deleted_successfully": "Shipping method deleted successfully", "discount_amount": "Discount Amount", "submit_request": "Submit Request", "enter_tax": "Enter tax", "enter_total_quantity": "Enter total quantity", "coupon_discount": "Coupon Discount", "assign": "Assign", "select_delivery_man": "Select Delivery Man", "stock_out_product": "Limited Stocks", "VIEW_ALL": "See All", "billing_address": "Billing Address", "order_note": "Order Note", "insufficient_balance": "Insufficient Balance", "payment_status": "Payment Status", "app_info": "App Info", "extra_discount": "Extra Discount", "minimum_amount": "Minimum amount is more than 1", "transaction_id": "Transaction", "refund": "Refund", "rejected": "Rejected", "refund_no": "Refund", "refunded": "Refunded", "product_price": "Product Price", "product_total_discount": "Product total discount", "product_total_tax": "Total tax", "total_refund_amount": "Total Refundable Amount", "select_refund_type": "Select refund status", "subtotal": "Sub Total", "refund_reason": "Refund Reason", "attachment": "Attachment", "successfully_updated_refund_status": "Successfully updated refund status", "approve": "Approve", "reject": "Reject", "are_you_sure_want_to_reject": "Are you sure want to reject?", "are_you_sure_want_to_approve": "Are you sure want to approve?", "refund_request": "Refund Request", "refund_details": "Refund Details", "refund_status_change_log": "Refund status changed log", "status": "Refund Request Status", "updated_by": "Updated By", "reason": "Reason", "product_details": "Product Details", "you_cant_override_admin_decision": "You can't override admin decision", "note": "Note", "note_required": "Note Required", "deliveryman_contact_details": "Delivery Man Information", "product_was_deleted": "Product was deleted", "shipping_cost": "Shipping Cost", "shipping_cost_multiply": "Multiply cost by quantity", "shipping_setting": "Shipping Setting", "shipping": "Shipping", "selected_product_wise_shipping": "You are selected product wise shipping", "category_cost_updated_successfully": "Category cost updated successfully", "shipping_method_updated_successfully": "Shipping method updated successfully", "by_self_delivery_man": "By self delivery man", "by_third_party_delivery_service": "By third party delivery service", "third_party_delivery_service": "Delivery service Name", "third_party_delivery_tracking_id": "Tracking Id", "shipping_info": "Shipping info", "add": "Add", "delivery_service_provider_name_required": "Delivery service provider name is required", "third_party_delivery_type_successfully": "Third party delivery type set successfully", "order_wise": "Order wise", "product_wise": "Product wise", "category_wise": "Category wise", "on_going_orders": "Ongoing Orders", "completed_orders": "Completed Orders", "qty": "Qty", "order_summery": "Order Summary", "change_status": "Change Status", "requested_on": "Requested on", "product_discount": "Product Discount", "product_tax": "Product Tax", "version": "version", "days_since_joined": "Days Since\nJoined", "light_theme": "Light Mode", "send_withdraw_request": "Send Withdraw Request", "filter": "Filter", "select_year": "Select year", "select_month": "Select Month", "transaction_length": "Transaction List", "reviews": "Reviews", "products": "Products", "inset_lang_wise_title_des": "Insert language wise product name & descriptions", "select_brand": "Select Brand", "select_category": "Select Category", "select_unit": "Select Unit", "add_color_variation": "Add Color Variation", "type_color_name": "Type Color Name", "other_attributes": "Other Attributes", "shipping_cost_multiply_by_item": "Shipping cost will by multiplied by the item quantity", "back": "Back", "product_seo_settings": "Product SEO Settings (Optional)", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "shipping_details": "Shipping Details", "select_a_brand": "Select a brand", "select_a_unit": "Select a unit", "enter_phone_number_for_password_reset": "Enter phone number for password reset", "enter_email_for_password_reset": "Please enter your registered email address to associate with your Vendor app account. We will send you a link to reset your password.", "ENTER_YOUR_EMAIL": "Enter your email", "send_otp": "Send OTP", "send_email": "Send Email", "PHONE_MUST_BE_REQUIRED": "Phone is required", "input_valid_phone_number": "Input valid phone number", "EMAIL_MUST_BE_REQUIRED": "Email is required", "sent": "<PERSON><PERSON>", "recovery_link_sent": "Recovery link sent", "please_enter_4_digit_code": "Please enter 4 digit code", "i_didnt_receive_the_code": " I did not receive the code", "resend_code": "Resend code", "verify": "Verify", "password_reset": "Password Reset", "new_password": "New password", "confirm_password": "Confirm Password", "reset_password": "Reset Password", "password_reset_successfully": "Password reset successfully", "input_valid_otp": "Input valid OTP", "review_details": "Review Details", "customer_account_was_deleted_you_cant_update_status": "Customer account has been deleted. you can't update status!", "re_enter_password": "Re-enter password", "password_be_at_least": "Password should be at least 6 character", "returned": "Returned", "canceled": "Canceled", "product_code_sku": "Product Code SKU", "generate_code": "Generate code", "product_code_is_required": "Product code is required", "review_status_updated_successfully": "Review status updated successfully", "product_code_minimum_6_digit": "Product code minimum 6 digit", "code": "Code", "maximum_quantity_270": "Maximum quantity 270", "generate": "Generate", "please_enter_from_1_to_270": "Please enter from 1 to 270", "download": "Download", "barcode_downloaded_successfully": "Barcode downloaded successfully", "reset": "Reset", "bar_code_generator": "Generate Bar Code", "new_request": "New Request", "minimum_order_quantity": "Min order quantity", "enter_minimum_order_quantity": "Enter minimum order quantity", "select_order_status": "Select order status", "physical": "Physical", "digital": "Digital", "product_type": "Product Type", "digital_product_type": "Digital Product Type", "upload_file": "Upload File", "ready_after_sell": "Ready after sell", "ready_product": "Ready product", "please_choose_digital_product": "Please choose digital product", "maximum_file_size_is": "Maximum file size is", "mb": "MB", "dont_have_an_account": "Don't have an account?", "registration": "Register", "seller_info": "Vendor Information", "shop_info": "Shop Information", "shop_application": "Seller Registration", "email_address": "Email Address", "phone_number": "Phone Number", "upload_image": "Upload image", "seller_profile": "Vendor profile", "business_or_shop_logo": "Business / Shop Logo", "business_or_shop_banner": "Business / Shop Banner", "i_agree_to_your": "I agree to Your", "shop_address": "Shop Address", "first_name_is_required": "First name is required", "last_name_is_required": "Last name is required", "email_is_required": "Email is required", "phone_is_required": "Phone is required", "phone_number_is_not_valid": "Phone number is not valid", "profile_image_is_required": "Profile image is required", "shop_name_is_required": "Shop name is required", "shop_address_is_required": "Shop address is required", "shop_logo_is_required": "Shop logo is required", "shop_banner_is_required": "Shop banner is required", "email_is_ot_valid": "Email is not valid", "password_is_required": "Password is required", "confirm_password_is_required": "Confirm password is required", "password_is_mismatch": "Password is mismatch", "password_minimum_length_is_6": "Password minimum length is 8", "you_are_successfully_registered": "You are successfully registered", "upload": "Upload", "choose_file": "Choose <PERSON>", "product_not_uploaded_yet": "Product not uploaded yet!", "digital_product_uploaded_successfully": "Digital product uploaded successfully", "are_you_sure_want_to_delete_this_shipping_method": "Are you sure? you want to delete this shipping method !", "shop_application_submission": "Shop Application Submission", "shop_register_message1": "Your shop application request has", "successfully": "successfully", "to_admin": "to admin", "shop_register_message2": "We will let you know the request update within 24 hours.", "delete_account": "Delete Account", "want_to_delete_account": "Are you sure, want to delete account?", "customer": "Customer", "delivery-man": "Delivery Man", "delivery_man_charge": "Deliveryman charge", "expected_delivery_date": "Expected delivery date", "delivery_man_charge_is_required": "Deliveryman charge is required", "manage_your_business_from_app": "Manage your business from app", "profile_image": "Profile Image", "earned": "Earned", "app_version": "App Version", "forget_password": "Forgot Password", "no_order_found": "No Order found", "no_refund_request_found": "No Refund Request Found", "edit_info": "Edit Bank Info", "ac_holder": "Holder Name", "bank": "Bank", "in_stock": "in stock", "update_quantity": "Update Quantity", "product_variations": "Product Variations", "choose_shipping": "Choose Shipping", "select_shipping_method": "Select your Preferred Shipping Method to Deliver Product", "select_language": "Select Language", "change_language": "Change language", "digital_payment": "Digitally Paid", "pay_by_wallet": "Pay by wallet", "pos_screen": "Pos Screen", "invoice": "Invoice", "sl": "SL", "product_info": "Product info", "total": "Total", "change": "Change", "terms_and_condition_details": "terms_and_condition_details", "powered_by": "Powered by", "shop_online": "Shop Online", "print": "Print", "print_invoice": "Print Invoice", "confirm_purchase": "Confirm Purchase", "pos": "POS", "billing_section": "Billing section", "add_customer": "Add Customer", "new_order": "New Order", "current_customer_status": "Current Customer", "clear_all_cart": "Clear all cart", "payment_via": "Payment Via", "item_info": "Item Info", "bill_summery": "Billing Summary", "edit_discount": "Edit Discount", "place_order": "Place Order", "vat": "Vat", "coupon": "Coupon", "coupon_code": "Coupon Code", "apply": "Apply", "discount_hint": "input value for discount", "discount_percentage": "Discount percentage", "top_selling_products": "Top Selling Products", "sold": "Sold", "most_popular_products": "Most Popular Products", "top_delivery_man": "Top Delivery Man", "order_delivered": "Order Delivered", "selling_price": "Selling <PERSON>", "add_new": "Add New", "limited_stocks": "Limited Stocks", "delivery": "Delivery", "delivery_man_list": "Delivery Man List", "delivery_man_setup": "Delivery Man Setup", "add_new_delivery_man": "Add New Delivery Man", "delivery_withdraws": " Delivery Withdraws", "emergency_contact_list": "Emergency contact List", "delivery_man_info": "Delivery Man Info", "add_delivery_man": "Add Delivery Man", "account_info": "Account Info", "online": "Online", "search": "Search", "delete": "Delete", "delivery_man_details": "Delivery Man Details", "overview": "Overview", "order_history": "Order History", "earnings": "Earnings", "xid": "XID", "withdraw_details": "Withdraw Details", "account_holder": "A/<PERSON> Holder", "deny": "<PERSON><PERSON>", "approve_this_withdraw_request": "Approve this withdraw request?", "deny_this_withdraw_request": "Deny this withdraw request?", "collect_cash": "Collect Cash", "cash_in_hand": "Cash In Hand", "withdrawable_balance": "Withdrawable Balance", "pending_withdraw": "Pending Withdrawn", "total_withdrawn": "Total Withdrawn", "details_information": "Details Information", "bank_information": "Bank Information", "already_withdrawn": "Already Withdrawn", "earning_statement": "Earning Statement", "emergency_contact": "Emergency Contact", "add_new_emergency_contact": "Add New Emergency Contact", "contact_name": "Contact Name", "cost_per_product": "Cost Per Product", "multiply_with_qty": "Multiply With QTY?", "action": "Action", "product_wise_delivery_note": "Please make sure all the product’s delivery charges are up to date.", "product_list": "Product List", "added_cart_successfully": "Added cart successfully", "product_specification": "Product Specification", "current_stock": "Current Stock", "description": "Description", "product_quantity_is_required": "Product quantity is required", "quantity_updated_successfully": "Quantity updated successfully", "customer_not_available": "Customer not available", "days_ago": "day's ago", "today": "Today", "poor": "Poor", "below_average": "Below Average", "average": "Average", "good": "Good", "excellent": "Excellent", "product_reviews": "Product Reviews", "change_log": "Change Log", "select_variant": "Select Variant", "total_price": "Total Price", "out_of_stock": "Out of Stock", "add_to_cart": "Add to Cart", "added_to_cart": "Added to <PERSON><PERSON>", "available": "Available", "you_got": "You got", "order_placed_successfully": "Order Placed Successfully", "delivery_man_added_successfully": "Delivery man added successfully", "identity_number": "Identity Number", "address_is_required": "Address is required", "identity_number_is_required": "Identity number is required", "identity_image_is_required": "Identity image is required", "offline": "Offline", "delivery_man_updated_successfully": "Delivery man updated successfully", "collect_cash_from_delivery_man": "Collect cash from delivery man", "receive": "Receive", "total_cash_in_hand": "Total cash in hand", "amount_is_required": "Amount is required", "amount_collected_from_deliveryman": "Amount collected from delivery man successfully", "status_updated_successfully": "Status updated successfully", "contact_updated_successfully": "Contact updated successfully", "contact_added_successfully": "Contact added successfully", "withdraw_list": "Withdraw List", "accepted": "Accepted", "add_new_customer": "Add New Customer", "country": "Country", "category_wise_filter": "Category wise Filter", "remove_from_cart_successfully": "Remove from cart successfully", "please_select_a_category_to_filter": "Please select a category to filter", "thank_you": "THANK YOU", "paid_by": "Paid by", "cash": "Cash", "card": "Card", "registration_here": "Registration Here", "image_size": "Image Size", "top_delivery_man_list": "Top DeliveryMan List", "variation": "Variation", "delivery_man_assign_successfully": "Deliveryman assign successfully", "payment_status_updated_successfully": "Payment status updated successfully", "withdraw_request_sent_successfully": "Withdraw request sent successfully", "customer_information": "Customer Information", "save_update": "Save Update", "shipping_method_title": "Shipping Method Title", "enter_shipping_method_title": "Ex: Inner City", "scan_item_or_add_from_item": "Scan Items or\nAdd From Items", "please_select_at_least_one_product": "Please select at least one product", "minimum_quantity_1": "Minimum quantity is 1", "percent": "Percent", "extra_discount_added_successfully": "Extra discount added successfully", "quantity_updated": "Quantity updated", "deliveryman": "Delivery Man", "select_identity_type": "Select Identity Type", "identity_image": "Identity Image", "available_balance": "Available Balance", "type_here": "Type here...", "change_something": "Change something to update", "bank_info_updated_successfully": "Bank info updated successfully", "history_of_order_no": "History of Order No", "this_year": "This Year", "this_month": "This Month", "this_week": "This Week", "show_on_map": "Show on Map", "paid": "Paid", "unpaid": "Unpaid", "deliveryman_information": "DeliveryMan Information", "order_setup": "Order Setup", "order_status": "Order Status", "delivery_type": "Delivery Type", "seller": "<PERSON><PERSON><PERSON>", "app": "APP", "invalid_credential_or_account_not_verified_yet": "Invalid credential or account not verified yet", "been_sent": "been sent", "sslcommerz": "Ssl commerz", "senang": "<PERSON><PERSON>", "paymob": "Paymob", "mercadopago": "Mercadopago", "stripe": "Stripe", "flutterwave": "Flutterwave", "paytm": "Paytm", "payStack": "PayStack", "payTabs": "PayTabs", "razorpay": "Razorpay", "bkAsh": "BkAsh", "paypal": "<PERSON><PERSON>", "liqpay": "Liqpay", "business_analytics": "Business Analytics", "overall": "OverAll", "see_more": "See More", "items_price": "Items Price", "sorry_coupon_is_not_valid": "Sorry, coupon is not valid", "discount_cant_greater_than_order_amount": "Discount can't greater than order amount", "coupons": "Coupons", "coupon_setup": "Coupon Setup", "coupon_type": "Coupon Type", "discount_on_purchase": "Discount on Purchase", "free_delivery": "Free Delivery", "stock_out": "Stock Out", "select_delivery_type": "Select Delivery Type", "additional_delivery_man_fee": "Deliveryman incentive", "filter_date": "Filter Data", "from": "From", "to": "To", "select_date": "Select Date", "select_status": "Select Status", "active": "Active", "inactive": "Inactive", "select_customer": "Select Customer", "coupon_title": "Coupon Title", "limit_for_same_user": "Limit For Same User", "percentage": "Percentage %", "minimum_purchase": "Minimum Purchase", "start_date": "Start Date", "expire_date": "Expire Date", "coupon_title_hint": "Title", "limit_user_hint": "EX: 10", "discount_amount_hint": "EX: 500", "minimum_purchase_hint": "EX: 100", "coupon_list": "Coupon List", "coupon_status_update_successfully": "Coupon Status Updated Successfully", "coupon_deleted_successfully": "Coupon Deleted Successfully", "coupon_added_successfully": "Coupon Added Successfully", "coupon_title_is_required": "Coupon title is required", "coupon_code_is_required": "Coupon Code is required", "limit_is_required": "Limit is required", "minimum_purchase_is_required": "Minimum purchase is required", "start_date_is_required": "Start date is required", "end_date_is_required": "End date is required", "coupon_updated_successfully": "Coupon updated successfully", "third_party_information": "Third Party Delivery Service Information", "refund_policy": "Refund Policy", "return_policy": "Return Policy", "cancellation_policy": "Cancellation Policy", "contact_name_is_required": "Contact Name is Required", "maximum_discount": "Maximum Discount", "start_from": "Start From", "expires_on": "Expires On", "off": "OFF", "edit_shop_info": "Edit Shop info", "enter_contact_number": "Enter contact number", "search_customer": "Search customer", "coupon_tooltip": "Some status buttons are disabled because the admin added coupons, the coupon discount bearer is admin, or some coupons are for all Vendors.", "paystack": "Paystack", "tax_model": "Tax Model", "temporary_close": "Temporary Close", "go_to_vacation_mode": "Go To Vacation Mode", "vacation": "Vacation", "temporary_close_message": "Are you sure? You want to Change your shop Temporary Close Status.", "vacation_note": "Vacation Note", "vacation_message": "Are you sure? You want to Change your shop Vacation Status.", "select_a_method": "Select a Method", "withdraw_balance": "Withdraw Balance", "please_fill_all_the_field": "Please fill all the field", "offline_payment": "Offline Payment", "please_select_vacation_date_range": "Please select vacation date range", "tags": "Tags", "vacation_date_range": "Date Range", "taxModel": "Tax Model", "include": "Include", "exclude": "Exclude", "image_upload_failed": "Image upload failed", "product_add_failed": "Product add failed", "store_secondary_banner": "Store secondary banner", "secondary_banner_is_required": "Secondary banner is required", "no_product_found": "No product found", "max_discount_is_required": "Max discount is required", "end_date": "End Date", "offer_banner": "Offer banner", "address_info": "Address Info", "your_order_is": "Your Order is", "billing": "Billing", "zip": "Zip", "contact_person_name": "Contact person name", "enter_contact_person_name": "Enter contact person name", "contact_person_number": "Contact person number", "enter_contact_person_number": "Enter contact person number", "update_address": "Update Address", "address_updated_successfully": "Address updated successfully", "minimum_order_amount": "Minimum order amount", "enter_minimum_order_amount": "123", "free_delivery_over_amount": "Free delivery over amount", "contact_person_email": "Contact person email", "enter_password": "Enter password", "my_payment_info": "My payment info", "customer_payment_info": "Customer payment info", "payment_by": "Payment By", "reference_id": "Reference ID", "enter_minimum_order_amount_or_free_delivery_over_amount": "Enter minimum order amount or free delivery over amount", "order_placed": "Order Placed", "vacation_mode": "Vacation Mode", "guest_customer": "Guest Customer", "order_verification_code": "Order Verification Code", "search_location": "Search Location", "select_location": "Select Location", "notification": "Notification", "notification_message": "Hello Sir/Mam We have updated our website theme! Please take a moment to review the changes in your shop.If you come across any image size issues kindly resize and upload them to ensure a refreshed look for your shop.", "notification_note": "Your attention to detail and effort in maintaining the visual integrity of our website are invaluable.", "switched": "Switched", "visit": "Visit Store", "completed_service_picture": "Completed Service’s Picture", "your_account_is_in_review_process": "Your account is in review process, please wait for admin approval", "invalid_credential": "Invalid credential", "account_not_verified_yet": "Account not verified yet", "new_order_added_successfully": "New order added successfully", "8_or_more_character": "8 or more character", "1_number": "1 number", "1_upper_case": "1 upper case", "1_lower_case": "1 lower case", "1_special_character": "1 special character", "receive_amount_cantbe_more_then_cash_in_hand": "Receive amount can't be more then cash in hand", "driving_license": "Driving Licence", "enter_free_delivery_over_amount": "Enter free delivery over amount", "no_deliveryman_found": "No Deliveryman Found", "you_should_fill_up": "You should fill-up this information correctly as this will be helpful for future transactions.", "if_once_you_delete_your": "If once you delete your vendor account, you’ll be lost your vendor forever.", "create_an_account": "Create an Account", "email_hint": "Ex: <EMAIL>", "mobile_hint": "Ex: 01xxxxxxxxx", "enter_your_password": "Enter your password", "proceed_to_next": "Proceed to Next", "seller_information": "Seller Information", "first_name_hint": "Ex: <PERSON><PERSON>", "last_name_hint": "Ex: <PERSON><PERSON><PERSON>", "seller_image": "SELLER IMAGE", "image_ratio": "Image Ratio 1:1", "image_size_2_mb": "Image Size : Max 2 MB", "store_name_hint": "Ex: XYZ store", "address_hint": "Write address", "store_image": "STORE IMAGE", "image_ratio3": "Image Ratio 3:1", "store_banner": "STORE BANNER", "secondary_banner": "STORE SECONDARY BANNER", "warning": "Warning!", "please_check_before_delete_your": "Please check before delete your vendor, if there is ongoing orders or admin and deliveryman transaction dependency. If have then complete those, otherwise can’t delete your account.", "upload_product_color_image": "Upload product color image", "variation_quantity_is_required": "Variation quantity is required", "amount_should_be": "Amount should be less then withdrawable balance", "there_isnt_enough_quantity": "There isn’t enough quantity on stock. Please check products in limited stock.", "more_products_have_low_stock": " + more products have low stock", "dont_show_again": "Don’t show again", "click_to_view": "Click to View", "this_product_is_low": "This product is low on stock", "discount_amount_should_be_less_then": "Discount should be less then 100 percent", "enter_valid_password": "Enter valid password", "please_enter_amount": "Please enter amount", "enter_shipping_cost": "Enter shipping cost", "shipping_cost_must_be_gater_then": "Shipping cost must be gater then 0", "review_id": "Review ID", "view_reply": "View Reply", "review_reply": "Review Reply", "write_your_reply_here": "Write your reply here..", "update_reply": "Update Reply", "write_a_review_reply": "Write a review reply", "reply_added_successfully": "Reply added successfully", "variation_setup": "Variation Setup", "product_seo": "Product SEO", "general_setup": "General Setup", "file_type": "File Type", "extension": "Extension", "file_upload": "Upload File", "file": "File", "sku": "SKU", "filetype_already_exists": "Selected file type already exists", "digital_product_file_empty": "Digital product file is empty", "digital_product_price_empty": "Digital product price is empty", "digital_product_sku_empty": "Digital product SKU is empty", "extension_already_exists": "Extension already exists", "digital_product_variation_empty": "Digital product variation is empty", "can_not_select_more_than": "Cannot select more than", "total_images_size_can_not_be_more_than": "Total file size can not be more than", "single_file_size_can_not_be_more_than": "Single file size can not be more than", "digital_product_deleted_successfully": "Digital product deleted successfully", "index": "Index", "no_follow": "No Follow", "no_image_index": "No Image Index", "no_index": "No Index", "no_archive": "No Archive", "no_snippet": "No Snippet", "max_snippet": "<PERSON>", "max_video_preview": "Max Video Preview", "max_image_preview": "Max Image Preview", "large": "Large", "medium": "Medium", "small": "Small", "sent_attachment": "Sent Attachment", "file_type_should_be": "File Type should be doc, docx, txt, csv, xls, xlsx, rar, tar, targz, zip, pdf", "view_more": "View more", "view_less": "View less", "coupon_code_hint": "Enter your coupon code", "do_you_want_to_exit_the_app": "Do you want to exit the app", "exit_app": "Exit App", "product_downloaded_successfully": "Product downloaded successfully", "product_download_failed": "Product download failed", "file_already_downloaded": "File already downloaded", "please_enable_your_location_and_bluetooth_in_your_system": "Please enable your location and bluetooth in your system.", "no_thermal_printer_connected": "No thermal printer connected", "paired_bluetooth": "Paired Bluetooth", "click_to_connect": "Click to connect", "printing": "Printing..", "any_query_feel_free_to_call": "Any query? Feel free to call or mail Us", "upload_preview_file": "Upload Preview File", "upload_a_suitable_file": "Upload a suitable file for a short product preview. This preview will be common for all variations.", "author_creator_artist": "Author/Creator/Artist", "publishing_house": "Publishing House", "see_preview": "See Preview", "download_preview_file": "Download Preview File.", "download_now": "Download Now", "select_all_fields": "Select all fields", "minimum_purchase_amount_should_be_greater_than_the_discount_amount": "Minimum purchase amount should be greater than discount amount", "order_is_delivered_you_cant_change_delivery_man": "Order is delivered you can't change delivery man", "order_is_already_delivered": "Order is already delivered", "maintenance_title": "We are Working On Something Special!", "maintenance_body": "We apologize for any inconvenience. For immediate assistance, please contact with our support team", "paid_amount": "<PERSON><PERSON>", "change_amount": "Change Amount", "paid_amount_cannot_less_then_order_amount": "Paid amount should not less then order amount", "restock": "Restock", "request_restock_request": "Request Restock List", "product_not_found": "Product not found", "total_request": "Total Request", "last_request": "Last request", "main_stock": "Main Stock", "stock": "Stock", "restoke_request_date": "Restoke Request Date", "select_date_or_brand": "select_date_or_brand", "search_products": "Search Products", "enter_stock_quantity": "Enter stock quantity", "quantity_should_grater_then_zero": "Quantity should greater then zero", "type_something_to_search_for_products": "Type something to search for products", "paid_amount_cannot_zero": "Paid amount should not zero", "your_wallet_balance_is_less_then_order_amount": "Your wallet balance is less then order amount", "please_add_color_image": "Please add color image", "clearance_sale": "Clearance Sale", "active_clearance_sale_offer": "Active Clearance Sale Offer", "show_your_offer_in_the_store_details": "Show your offer in the store details page in customer website and apps", "setup_offer_logic": "Setup Offer Logic", "flat_discount": "Flat Discount", "product_wise_discount": "Product Wise Discount", "offer_active_time": "Offer Active Time", "always_active": "Always Active", "specific_time_in_a_day": "Specific Time in a Day", "pick_time": "Pick Time", "till": "<PERSON>", "open_time": "Open Time", "close_time": "Close Time", "statuss": "Status", "edit_item": "<PERSON>em", "clear_all": "Clear All", "enter_somethings": "Please input product name and then search", "add_products": "Add products", "are_you_sure_remove_this_item": "Are you sure, want to remove this item?", "are_you_sure_remove_all_item": "Are you sure, want to remove All item?", "once_you_remove_the_item": "Once you remove the item from the list, it will be no longer available in clearance sale.", "once_you_remove_all_item": "Once you remove all item from the list, they will be no longer available in clearance sale.", "discount_amount_is": "Discount amount is Empty", "discount_cannot_grater_then": "Discount cannot grater then 100%", "discount_amount_cannot_grater_then_product_price": "Discount amount cannot grater then product price", "discount_amount_cannot_less_product_price": "Discount amount cannot less then product price", "some_product_contains_wrong_amount": "Some product contains wrong amount", "clearance_sale_is_deactivate": "Clearance sale is deactivate", "end_date_should_not_before_start_date": "End date should not before start date", "add_product_show_in_the_clearance": "Add Product show in the clearance offer section in customer app & website", "please_setup_the_configuration_first": "Please setup the configuration first", "hold": "Hold", "hold_orders": "Hold Orders", "your_hold_orders": "Your hold orders", "search_by_customer_name": "Search by customer name...", "hold_Id": "Hold ID: ", "resume": "Resume", "clear": "Clear", "no_item_in_your_cart": "No item in your cart", "end_time_cannot_be": "End time cannot be before or same as start time", "select_start_and_end_time": "Please select start and end time", "delete_product": "Delete product", "product_discount_updated": "Product discount updated", "no_hold_order": "No hold order", "your_products_unit": "Your products unit price is lower then offer price", "walking_customers_are_not": "Walking customers are not allowed to place orders containing digital products", "setup_updated_successfully": "Setup updated successfully", "no_category_found": "No Category Found", "pricing_and_others": "Pricing & Others", "select_tax_model": "Select Tax Model", "select_file_type": "Select file type", "variation_wise_file_upload": "Variation Wise File Upload", "price_s": "Price($)", "product_video_image": "Product Video & Image", "provide_embedded_link": "Please provide embedded link not direct link", "additional_product_images": "Additional Product Images", "go_back": "Go Back", "price_range": "Price Range", "max": "Max", "min": "Min", "clear_filter": "Clear Filter", "from_gallery": "From Gallery", "open_camera": "Open Camera", "please_ensure_the_deliveryman_has": "Please ensure the Deliveryman has", "in_change_ready_for_the_customer": "in change ready for the customer.", "loading": "Loading", "filter_data": "Filter Data", "search_by_product_name": "Search by product name", "no_customer_found": "No Customer Found", "created_at": "Created At", "publisher": "Publisher", "author": "Author", "view_all": "View All", "no_statistics_generated_yet": "No statistics generated yet", "general_information": "General Information", "product_unit": "Product Unit", "product_sku": "Product SKU", "price_information": "Price information", "edit_product": "Edit Product", "there_is_no_change_to_update": "There is no change to update", "please_select_delivery_man": "Please select delivery man", "please_enter_delivery_service_name": "Please enter delivery service name", "please_enter_tracking_id": "Please enter tracking id", "please_enter_delivery_incentive": "Please enter delivery man incentive", "update_cart": "Update Cart", "discount_amount_must_be_grater_then": "Discount amount must be grater then 0", "withdraw_amount_should_be_grater_then": "Withdraw amount should be grater then 0", "ENTER_MOBILE_NUMBER": "Phone number", "orders_with_digital_products": "Orders with digital products are not allowed for walking customers.", "no_results_found": "No results found", "unsaved_changes": "Unsaved Changes", "unsaved_changes_message": "You have unsaved changes. Are you sure you want to leave?", "leave": "Leave", "customers": "Customers", "customer_management": "Customer Management", "customer_details": "Customer Details", "customer_details_not_found": "Customer details not found", "personal_information": "Personal Information", "address_information": "Address Information", "customer_status": "Customer Status", "edit_customer": "Edit Customer", "update_customer": "Update Customer", "delete_customer": "Delete Customer", "search_by_name_email_phone": "Search by name, email, or phone", "no_customers_found": "No customers found", "load_more": "Load More", "country_code": "Country Code", "referral_code": "Referral Code", "joined_date": "Joined Date", "street_address": "Street Address", "orders_summary": "Orders Summary", "total_orders": "Total Orders", "recent_orders": "Recent Orders", "recent_orders_found": "recent orders found", "delete_customer_confirmation": "Are you sure you want to delete this customer?", "delete_customer_warning": "This action cannot be undone. All customer data will be permanently removed.", "customer_deleted_successfully": "Customer deleted successfully", "failed_to_delete_customer": "Failed to delete customer", "customer_added_successfully": "Customer added successfully!", "customer_add_failed": "Failed to add customer. Please try again.", "customer_updated_successfully": "Customer updated successfully", "failed_to_update_customer": "Failed to update customer", "enter_country_code": "e.g., +1", "enter_referral_code": "Enter referral code (optional)", "enter_country": "Enter country", "enter_city": "Enter city", "enter_zip": "Enter ZIP code", "enter_street_address": "Enter street address", "enter_phone": "Enter phone number", "retry": "Retry"}