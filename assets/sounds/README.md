# Audio Assets for Order Notifications

This directory contains audio files for different order notification types:

## Sound Files Required:

1. **notification.mp3** - Default notification sound
2. **order_confirmed.mp3** - Sound for order confirmation
3. **order_delivered.mp3** - Sound for order delivery
4. **order_alert.mp3** - Sound for order cancellation/failure
5. **new_order.mp3** - Sound for new order received

## Implementation Notes:

- All audio files should be in MP3 format for cross-platform compatibility
- Keep file sizes small (under 100KB) for quick loading
- Use appropriate volume levels that are noticeable but not jarring
- Consider different tones for different notification types:
  - Positive sounds for confirmations and deliveries
  - Alert sounds for cancellations and failures
  - Distinctive sound for new orders

## Adding Audio Files:

1. Place MP3 files in this directory
2. Update pubspec.yaml to include these assets
3. Audio files will be loaded using AssetSource in the OrderNotificationService

## Fallback:

If specific sound files are not found, the service will fall back to the default notification.mp3 file.
