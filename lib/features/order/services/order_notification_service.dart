import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';
import 'package:sixvalley_vendor_app/features/order/domain/models/order_model.dart';
import 'package:sixvalley_vendor_app/localization/language_constrants.dart';
import 'package:sixvalley_vendor_app/main.dart';

class OrderNotificationService {
  static final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = 
      FlutterLocalNotificationsPlugin();
  static final AudioPlayer _audioPlayer = AudioPlayer();

  static Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('notification_icon');
    
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );
    
    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    
    await _flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }

  static Future<void> showOrderStatusNotification({
    required int orderId,
    required String oldStatus,
    required String newStatus,
    required String customerName,
  }) async {
    try {
      final String title = _getNotificationTitle(newStatus);
      final String body = _getNotificationBody(orderId, newStatus, customerName);
      
      // Play notification sound
      await _playNotificationSound(newStatus);
      
      // Trigger haptic feedback
      await _triggerHapticFeedback(newStatus);
      
      // Show local notification
      await _showLocalNotification(
        id: orderId,
        title: title,
        body: body,
        payload: 'order_$orderId',
      );
      
      if (kDebugMode) {
        debugPrint('Order notification sent: Order #$orderId status changed from $oldStatus to $newStatus');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error sending order notification: $e');
      }
    }
  }

  static String _getNotificationTitle(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return getTranslated('order_confirmed', Get.context!) ?? 'Order Confirmed';
      case 'processing':
        return getTranslated('order_processing', Get.context!) ?? 'Order Processing';
      case 'out_for_delivery':
        return getTranslated('order_out_for_delivery', Get.context!) ?? 'Order Out for Delivery';
      case 'delivered':
        return getTranslated('order_delivered', Get.context!) ?? 'Order Delivered';
      case 'canceled':
        return getTranslated('order_canceled', Get.context!) ?? 'Order Canceled';
      case 'returned':
        return getTranslated('order_returned', Get.context!) ?? 'Order Returned';
      case 'failed':
        return getTranslated('order_failed', Get.context!) ?? 'Order Failed';
      default:
        return getTranslated('order_status_updated', Get.context!) ?? 'Order Status Updated';
    }
  }

  static String _getNotificationBody(int orderId, String status, String customerName) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'Order #$orderId for $customerName has been confirmed and is ready for processing.';
      case 'processing':
        return 'Order #$orderId for $customerName is now being processed.';
      case 'out_for_delivery':
        return 'Order #$orderId for $customerName is out for delivery.';
      case 'delivered':
        return 'Order #$orderId for $customerName has been successfully delivered.';
      case 'canceled':
        return 'Order #$orderId for $customerName has been canceled.';
      case 'returned':
        return 'Order #$orderId for $customerName has been returned.';
      case 'failed':
        return 'Order #$orderId for $customerName has failed.';
      default:
        return 'Order #$orderId for $customerName status has been updated to $status.';
    }
  }

  static Future<void> _playNotificationSound(String status) async {
    try {
      String soundFile;
      switch (status.toLowerCase()) {
        case 'confirmed':
        case 'processing':
          soundFile = 'sounds/order_confirmed.mp3';
          break;
        case 'delivered':
          soundFile = 'sounds/order_delivered.mp3';
          break;
        case 'canceled':
        case 'failed':
        case 'returned':
          soundFile = 'sounds/order_alert.mp3';
          break;
        default:
          soundFile = 'sounds/notification.mp3';
          break;
      }
      
      await _audioPlayer.play(AssetSource(soundFile));
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error playing notification sound: $e');
      }
      // Fallback to system notification sound
      await _audioPlayer.play(AssetSource('sounds/notification.mp3'));
    }
  }

  static Future<void> _triggerHapticFeedback(String status) async {
    try {
      switch (status.toLowerCase()) {
        case 'confirmed':
        case 'processing':
          await HapticFeedback.lightImpact();
          break;
        case 'delivered':
          await HapticFeedback.mediumImpact();
          break;
        case 'canceled':
        case 'failed':
        case 'returned':
          await HapticFeedback.heavyImpact();
          break;
        default:
          await HapticFeedback.selectionClick();
          break;
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error triggering haptic feedback: $e');
      }
    }
  }

  static Future<void> _showLocalNotification({
    required int id,
    required String title,
    required String body,
    required String payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'order_notifications',
      'Order Notifications',
      channelDescription: 'Notifications for order status updates',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  static Future<void> showNewOrderNotification({
    required Order order,
  }) async {
    try {
      final String title = getTranslated('new_order_received', Get.context!) ?? 'New Order Received';
      final String body = 'New order #${order.id} from ${order.customer?.fName ?? 'Customer'} for ${order.orderAmount} received.';
      
      // Play new order sound
      await _audioPlayer.play(AssetSource('sounds/new_order.mp3'));
      
      // Strong haptic feedback for new orders
      await HapticFeedback.heavyImpact();
      
      await _showLocalNotification(
        id: order.id ?? 0,
        title: title,
        body: body,
        payload: 'new_order_${order.id}',
      );
      
      if (kDebugMode) {
        debugPrint('New order notification sent: Order #${order.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error sending new order notification: $e');
      }
    }
  }

  static Future<void> requestPermissions() async {
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();
  }
}
