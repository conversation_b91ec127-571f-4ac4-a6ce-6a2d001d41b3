
class DeliveryManBody {
  int? id;
  String? fName;
  String? lName;
  String? address;
  String? phone;
  String? email;
  String? countryCode;
  String? identityNumber;
  String? identityType;
  String? password;
  String? confirmPassword;

  DeliveryManBody(
      {this.id,
        this.fName,
      this.lName,
      this.address,
      this.phone,
      this.email,
      this.countryCode,
      this.identityNumber,
      this.identityType,
      this.password,
      this.confirmPassword});
}

