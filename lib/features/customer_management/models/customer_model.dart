class CustomerModel {
  final int id;
  final String name;
  final String? fName;
  final String? lName;
  final String email;
  final String phone;
  final String? countryCode;
  final String? image;
  final int? isActive;
  final String? referralCode;
  final String? createdAt;
  final String? updatedAt;
  final int? ordersCount;
  final List<dynamic>? orders;
  final String? country;
  final String? city;
  final String? zip;
  final String? streetAddress;

  CustomerModel({
    required this.id,
    required this.name,
    this.fName,
    this.lName,
    required this.email,
    required this.phone,
    this.countryCode,
    this.image,
    this.isActive,
    this.referralCode,
    this.createdAt,
    this.updatedAt,
    this.ordersCount,
    this.orders,
    this.country,
    this.city,
    this.zip,
    this.streetAddress,
  });

  factory CustomerModel.fromJson(Map<String, dynamic> json) {
    try {
      // Construct full name from f_name and l_name if name is not provided
      String fullName = json['name'] ?? '';
      if (fullName.isEmpty) {
        String fName = json['f_name'] ?? '';
        String lName = json['l_name'] ?? '';
        fullName = '$fName $lName'.trim();
      }
      
      if (fullName.isEmpty) {
        fullName = 'Unknown Customer';
      }
      
      return CustomerModel(
        id: int.tryParse(json['id']?.toString() ?? '0') ?? 0,
        name: fullName,
        fName: json['f_name']?.toString(),
        lName: json['l_name']?.toString(),
        email: json['email']?.toString() ?? '',
        phone: json['phone']?.toString() ?? '',
        countryCode: json['country_code']?.toString(),
        image: json['image']?.toString(),
        isActive: parseActiveStatus(json['is_active']),
        referralCode: json['referral_code']?.toString(),
        createdAt: json['created_at']?.toString(),
        updatedAt: json['updated_at']?.toString(),
        ordersCount: int.tryParse(json['orders_count']?.toString() ?? '0'),
        orders: json['orders'] as List<dynamic>?,
        country: json['country']?.toString(),
        city: json['city']?.toString(),
        zip: json['zip']?.toString(),
        streetAddress: json['street_address']?.toString(),
      );
    } catch (e) {
      print('Error parsing CustomerModel: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  static int parseActiveStatus(dynamic value) {
    if (value == null) {
      return 1; // Default to active
    }
    
    // Handle string values like "1", "0", "true", "false", "active", "inactive"
    if (value is String) {
      String lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == 'active' || lowerValue == '1') {
        return 1;
      } else if (lowerValue == 'false' || lowerValue == 'inactive' || lowerValue == '0') {
        return 0;
      }
    }
    
    // Handle boolean values
    if (value is bool) {
      return value ? 1 : 0;
    }
    
    // Handle numeric values
    return int.tryParse(value.toString()) ?? 1;
  }

  bool get isCustomerActive {
    if (isActive == null) return true;
    return isActive! > 0;
  }
}

class CustomerCreateRequest {
  final String fName;
  final String lName;
  final String email;
  final String phone;
  final String password;
  final String? countryCode;
  final String? referralCode;

  CustomerCreateRequest({
    required this.fName,
    required this.lName,
    required this.email,
    required this.phone,
    required this.password,
    this.countryCode,
    this.referralCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'f_name': fName,
      'l_name': lName,
      'email': email,
      'phone': phone,
      'password': password,
      if (countryCode != null) 'country_code': countryCode,
      if (referralCode != null) 'referral_code': referralCode,
    };
  }
}

class CustomerUpdateRequest {
  final String fName;
  final String lName;
  final String email;
  final String phone;
  final String? countryCode;
  final bool? isActive;
  final String? country;
  final String? city;
  final String? zip;
  final String? streetAddress;
  final String? image;

  CustomerUpdateRequest({
    required this.fName,
    required this.lName,
    required this.email,
    required this.phone,
    this.countryCode,
    this.isActive,
    this.country,
    this.city,
    this.zip,
    this.streetAddress,
    this.image,
  });

  Map<String, dynamic> toJson() {
    return {
      'f_name': fName,
      'l_name': lName,
      'email': email,
      'phone': phone,
      if (countryCode != null) 'country_code': countryCode,
      if (isActive != null) 'is_active': isActive,
      if (country != null) 'country': country,
      if (city != null) 'city': city,
      if (zip != null) 'zip': zip,
      if (streetAddress != null) 'street_address': streetAddress,
      if (image != null) 'image': image,
    };
  }

  factory CustomerUpdateRequest.fromCustomer(CustomerModel customer) {
    return CustomerUpdateRequest(
      fName: customer.fName ?? '',
      lName: customer.lName ?? '',
      email: customer.email,
      phone: customer.phone,
      countryCode: customer.countryCode,
      isActive: customer.isCustomerActive,
      country: customer.country,
      city: customer.city,
      zip: customer.zip,
      streetAddress: customer.streetAddress,
    );
  }
}

class CustomerListResponse {
  final int totalSize;
  final int limit;
  final int offset;
  final List<CustomerModel> customers;

  CustomerListResponse({
    required this.totalSize,
    required this.limit,
    required this.offset,
    required this.customers,
  });

  factory CustomerListResponse.fromJson(Map<String, dynamic> json) {
    try {
      print('Parsing CustomerListResponse from: $json');
      
      List<CustomerModel> customersList = [];
      if (json['customers'] != null && json['customers'] is List) {
        for (var customerJson in json['customers']) {
          try {
            customersList.add(CustomerModel.fromJson(customerJson));
          } catch (e) {
            print('Error parsing individual customer: $e');
            print('Customer data: $customerJson');
          }
        }
      }
      
      return CustomerListResponse(
        totalSize: int.tryParse(json['total_size']?.toString() ?? json['totalSize']?.toString() ?? '0') ?? 0,
        limit: int.tryParse(json['limit']?.toString() ?? '15') ?? 15,
        offset: int.tryParse(json['offset']?.toString() ?? '1') ?? 1,
        customers: customersList,
      );
    } catch (e) {
      print('Error parsing CustomerListResponse: $e');
      print('JSON: $json');
      rethrow;
    }
  }
}
