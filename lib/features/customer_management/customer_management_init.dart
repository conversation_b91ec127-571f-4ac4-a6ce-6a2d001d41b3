import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sixvalley_vendor_app/features/customer_management/data/customer_repository.dart';
import 'package:sixvalley_vendor_app/features/customer_management/domain/services/customer_service.dart';
import 'package:sixvalley_vendor_app/features/customer_management/provider/customer_provider.dart';
import 'package:sixvalley_vendor_app/di_container.dart' as di;

class CustomerManagementInit extends StatelessWidget {
  final Widget child;
  const CustomerManagementInit({required this.child, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => CustomerProvider(
            customerServiceInterface: CustomerService(
              customerRepositoryInterface: CustomerRepository(
                dioClient: di.sl(),
              ),
            ),
          ),
        ),
      ],
      child: child,
    );
  }
}
