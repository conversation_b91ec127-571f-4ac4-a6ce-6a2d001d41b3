import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sixvalley_vendor_app/features/customer_management/customer_management_init.dart';
import 'package:sixvalley_vendor_app/features/customer_management/screens/customer_list_screen.dart';

/// Test screen to verify customer management integration
class CustomerManagementTestScreen extends StatelessWidget {
  const CustomerManagementTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Management Test'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => CustomerManagementInit(
                  child: CustomerListScreen(),
                ),
              ),
            );
          },
          child: const Text('Open Customer Management'),
        ),
      ),
    );
  }
}
