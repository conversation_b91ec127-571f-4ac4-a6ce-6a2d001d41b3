import 'package:sixvalley_vendor_app/data/model/response/base/api_response.dart';
import 'package:sixvalley_vendor_app/features/customer_management/domain/repositories/customer_repository_interface.dart';
import 'package:sixvalley_vendor_app/features/customer_management/domain/services/customer_service_interface.dart';
import '../../models/customer_model.dart';

class CustomerService implements CustomerServiceInterface {
  final CustomerRepositoryInterface customerRepositoryInterface;
  CustomerService({required this.customerRepositoryInterface});

  @override
  Future<ApiResponse> getCustomerList({String? search, int limit = 15, int offset = 1}) async {
    return await customerRepositoryInterface.getCustomerList(search: search, limit: limit, offset: offset);
  }

  @override
  Future<ApiResponse> createCustomer(CustomerCreateRequest request) async {
    return await customerRepositoryInterface.createCustomer(request);
  }

  @override
  Future<ApiResponse> getCustomerDetails(int customerId) async {
    return await customerRepositoryInterface.getCustomerDetails(customerId);
  }

  @override
  Future<ApiResponse> updateCustomer(int customerId, CustomerUpdateRequest request) async {
    return await customerRepositoryInterface.updateCustomer(customerId, request);
  }

  @override
  Future<ApiResponse> deleteCustomer(int customerId) async {
    return await customerRepositoryInterface.deleteCustomer(customerId);
  }
}
