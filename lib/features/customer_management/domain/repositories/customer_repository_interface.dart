import 'package:sixvalley_vendor_app/data/model/response/base/api_response.dart';
import '../../models/customer_model.dart';

abstract class CustomerRepositoryInterface {
  Future<ApiResponse> getCustomerList({String? search, int limit = 15, int offset = 1});
  Future<ApiResponse> createCustomer(CustomerCreateRequest request);
  Future<ApiResponse> getCustomerDetails(int customerId);
  Future<ApiResponse> updateCustomer(int customerId, CustomerUpdateRequest request);
  Future<ApiResponse> deleteCustomer(int customerId);
}
