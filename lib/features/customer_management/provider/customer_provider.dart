import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sixvalley_vendor_app/data/model/response/base/api_response.dart';
import 'package:sixvalley_vendor_app/features/customer_management/domain/services/customer_service_interface.dart';
import 'package:sixvalley_vendor_app/features/customer_management/models/customer_model.dart';
import 'package:sixvalley_vendor_app/helper/api_checker.dart';

class CustomerProvider extends ChangeNotifier {
  final CustomerServiceInterface customerServiceInterface;
  CustomerProvider({required this.customerServiceInterface});

  List<CustomerModel> _customers = [];
  int _totalCount = 0;
  bool _isLoading = false;
  bool _isCreating = false;
  bool _isLoadingDetails = false;
  bool _isUpdating = false;
  bool _isDeleting = false;
  CustomerModel? _customerDetails;
  String _search = '';
  int _limit = 15;
  int _offset = 1;

  List<CustomerModel> get customers => _customers;
  int get totalCount => _totalCount;
  bool get isLoading => _isLoading;
  bool get isCreating => _isCreating;
  bool get isLoadingDetails => _isLoadingDetails;
  bool get isUpdating => _isUpdating;
  bool get isDeleting => _isDeleting;
  CustomerModel? get customerDetails => _customerDetails;
  String get search => _search;
  int get limit => _limit;

  Future<void> getCustomerList({String? search, int? limit, int? offset}) async {
    _isLoading = true;
    notifyListeners();
    try {
      ApiResponse apiResponse = await customerServiceInterface.getCustomerList(
        search: search ?? _search,
        limit: limit ?? _limit,
        offset: offset ?? _offset,
      );
      
      if (kDebugMode) {
        print('=== CUSTOMER API DEBUG ===');
        print('Response status: ${apiResponse.response?.statusCode}');
        print('Response data type: ${apiResponse.response?.data.runtimeType}');
        print('Response data: ${apiResponse.response?.data}');
        print('=========================');
      }
      
      if (apiResponse.response != null && apiResponse.response!.statusCode == 200) {
        if (offset == null || offset == 1) {
          _customers.clear();
        }
        
        try {
          // First try the expected format (like OrderModel)
          CustomerListResponse customerListResponse = CustomerListResponse.fromJson(apiResponse.response!.data);
          if (offset == null || offset == 1) {
            _customers.addAll(customerListResponse.customers);
          } else {
            _customers.addAll(customerListResponse.customers);
          }
          _totalCount = customerListResponse.totalSize;
        } catch (e) {
          if (kDebugMode) {
            print('Failed to parse as CustomerListResponse, trying alternative formats: $e');
          }
          
          // Fallback: Try direct parsing like we had before
          dynamic responseData = apiResponse.response?.data;
          
          if (responseData is Map<String, dynamic>) {
            List<dynamic>? customersList;
            
            // Try various possible keys
            if (responseData.containsKey('customers')) {
              customersList = responseData['customers'] as List<dynamic>?;
            } else if (responseData.containsKey('data')) {
              var dataValue = responseData['data'];
              if (dataValue is List) {
                customersList = dataValue;
              } else if (dataValue is Map && dataValue.containsKey('customers')) {
                customersList = dataValue['customers'] as List<dynamic>?;
              }
            }
            
            if (customersList != null) {
              for (var customerJson in customersList) {
                try {
                  CustomerModel customer = CustomerModel.fromJson(customerJson);
                  _customers.add(customer);
                } catch (e) {
                  if (kDebugMode) {
                    print('Error parsing customer: $e');
                    print('Customer JSON: $customerJson');
                  }
                }
              }
              _totalCount = responseData['total_size'] ?? responseData['total'] ?? customersList.length;
            }
          } else if (responseData is List) {
            // Direct list format
            for (var customerJson in responseData) {
              try {
                CustomerModel customer = CustomerModel.fromJson(customerJson);
                _customers.add(customer);
              } catch (e) {
                if (kDebugMode) {
                  print('Error parsing customer from list: $e');
                }
              }
            }
            _totalCount = _customers.length;
          }
        }
        
        _search = search ?? _search;
        _limit = limit ?? _limit;
        _offset = offset ?? _offset;
        
        if (kDebugMode) {
          print('Successfully parsed ${_customers.length} customers, total count: $_totalCount');
        }
      } else {
        if (kDebugMode) {
          print('API error or non-200 status: ${apiResponse.response?.statusCode}');
          print('Error message: ${apiResponse.error}');
        }
        ApiChecker.checkApi(apiResponse);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Customer list exception: $e');
      }
    }
    _isLoading = false;
    notifyListeners();
  }

  Future<bool> createCustomer(CustomerCreateRequest request) async {
    _isCreating = true;
    notifyListeners();
    
    try {
      ApiResponse apiResponse = await customerServiceInterface.createCustomer(request);
      
      if (kDebugMode) {
        print('=== CUSTOMER CREATE DEBUG ===');
        print('Response status: ${apiResponse.response?.statusCode}');
        print('Response data: ${apiResponse.response?.data}');
        print('==============================');
      }
      
      if (apiResponse.response != null && 
          (apiResponse.response!.statusCode == 200 || apiResponse.response!.statusCode == 201)) {
        // Refresh the customer list after successful creation
        await getCustomerList();
        _isCreating = false;
        notifyListeners();
        return true;
      } else {
        if (kDebugMode) {
          print('Customer creation failed with status: ${apiResponse.response?.statusCode}');
          print('Error: ${apiResponse.error}');
        }
        ApiChecker.checkApi(apiResponse);
        _isCreating = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Customer creation exception: $e');
      }
      _isCreating = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> getCustomerDetails(int customerId) async {
    _isLoadingDetails = true;
    _customerDetails = null;
    notifyListeners();
    
    try {
      ApiResponse apiResponse = await customerServiceInterface.getCustomerDetails(customerId);
      
      if (kDebugMode) {
        print('=== CUSTOMER DETAILS API DEBUG ===');
        print('Response status: ${apiResponse.response?.statusCode}');
        print('Response data: ${apiResponse.response?.data}');
        print('==================================');
      }
      
      if (apiResponse.response != null && apiResponse.response!.statusCode == 200) {
        try {
          dynamic responseData = apiResponse.response!.data;
          
          if (responseData is Map<String, dynamic>) {
            // Check if customer data is directly in response or nested
            Map<String, dynamic>? customerData;
            
            if (responseData.containsKey('customer')) {
              customerData = responseData['customer'] as Map<String, dynamic>?;
            } else {
              customerData = responseData;
            }
            
            if (customerData != null) {
              _customerDetails = CustomerModel.fromJson(customerData);
              if (kDebugMode) {
                print('Successfully parsed customer details: ${_customerDetails?.name}');
              }
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing customer details: $e');
          }
        }
      } else {
        if (kDebugMode) {
          print('Customer details API error: ${apiResponse.response?.statusCode}');
          print('Error message: ${apiResponse.error}');
        }
        ApiChecker.checkApi(apiResponse);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Customer details exception: $e');
      }
    }
    
    _isLoadingDetails = false;
    notifyListeners();
  }

  Future<bool> updateCustomer(int customerId, CustomerUpdateRequest request) async {
    _isUpdating = true;
    notifyListeners();
    
    try {
      ApiResponse apiResponse = await customerServiceInterface.updateCustomer(customerId, request);
      
      if (kDebugMode) {
        print('=== CUSTOMER UPDATE DEBUG ===');
        print('Response status: ${apiResponse.response?.statusCode}');
        print('Response data: ${apiResponse.response?.data}');
        print('=============================');
      }
      
      if (apiResponse.response != null && 
          (apiResponse.response!.statusCode == 200 || apiResponse.response!.statusCode == 201)) {
        // Refresh the customer details after successful update
        await getCustomerDetails(customerId);
        // Also refresh the customer list to reflect changes
        await getCustomerList();
        _isUpdating = false;
        notifyListeners();
        return true;
      } else {
        if (kDebugMode) {
          print('Customer update failed with status: ${apiResponse.response?.statusCode}');
          print('Error: ${apiResponse.error}');
        }
        ApiChecker.checkApi(apiResponse);
        _isUpdating = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Customer update exception: $e');
      }
      _isUpdating = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> deleteCustomer(int customerId) async {
    _isDeleting = true;
    notifyListeners();
    
    try {
      ApiResponse apiResponse = await customerServiceInterface.deleteCustomer(customerId);
      
      if (kDebugMode) {
        print('=== CUSTOMER DELETE DEBUG ===');
        print('Response status: ${apiResponse.response?.statusCode}');
        print('Response data: ${apiResponse.response?.data}');
        print('=============================');
      }
      
      if (apiResponse.response != null && 
          (apiResponse.response!.statusCode == 200 || apiResponse.response!.statusCode == 204)) {
        // Remove the customer from local list if it exists
        _customers.removeWhere((customer) => customer.id == customerId);
        _totalCount = _totalCount > 0 ? _totalCount - 1 : 0;
        
        // Clear customer details if it was the deleted customer
        if (_customerDetails?.id == customerId) {
          _customerDetails = null;
        }
        
        _isDeleting = false;
        notifyListeners();
        return true;
      } else {
        if (kDebugMode) {
          print('Customer delete failed with status: ${apiResponse.response?.statusCode}');
          print('Error: ${apiResponse.error}');
        }
        ApiChecker.checkApi(apiResponse);
        _isDeleting = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Customer delete exception: $e');
      }
      _isDeleting = false;
      notifyListeners();
      return false;
    }
  }
}
