import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sixvalley_vendor_app/features/customer_management/provider/customer_provider.dart';

class TestDeleteFlow extends StatelessWidget {
  const TestDeleteFlow({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Delete Flow'),
      ),
      body: Consumer<CustomerProvider>(
        builder: (context, customerProvider, _) {
          return Column(
            children: [
              Text('Customer count: ${customerProvider.customers.length}'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () async {
                  print('=== TESTING DELETE FLOW ===');
                  print('Before delete - customers count: ${customerProvider.customers.length}');
                  
                  if (customerProvider.customers.isNotEmpty) {
                    final firstCustomer = customerProvider.customers.first;
                    print('Deleting customer: ${firstCustomer.id} - ${firstCustomer.name}');
                    
                    final success = await customerProvider.deleteCustomer(firstCustomer.id);
                    print('Delete result: $success');
                    print('After delete - customers count: ${customerProvider.customers.length}');
                    
                    if (success) {
                      print('Refreshing customer list...');
                      await customerProvider.getCustomerList();
                      print('After refresh - customers count: ${customerProvider.customers.length}');
                    }
                  }
                },
                child: const Text('Test Delete First Customer'),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () async {
                  print('Refreshing customer list manually...');
                  await customerProvider.getCustomerList();
                  print('Manual refresh complete - customers count: ${customerProvider.customers.length}');
                },
                child: const Text('Manual Refresh'),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: customerProvider.customers.length,
                  itemBuilder: (context, index) {
                    final customer = customerProvider.customers[index];
                    return ListTile(
                      title: Text(customer.name),
                      subtitle: Text('ID: ${customer.id}'),
                      trailing: Text(customer.isCustomerActive ? 'Active' : 'Inactive'),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
