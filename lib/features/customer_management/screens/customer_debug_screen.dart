import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sixvalley_vendor_app/features/customer_management/provider/customer_provider.dart';
import 'package:sixvalley_vendor_app/features/customer_management/models/customer_model.dart';

class CustomerDebugScreen extends StatelessWidget {
  const CustomerDebugScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Debug'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Consumer<CustomerProvider>(
          builder: (context, controller, _) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ElevatedButton(
                  onPressed: () async {
                    await controller.getCustomerList();
                  },
                  child: const Text('Test Customer List API'),
                ),
                const SizedBox(height: 10),
                ElevatedButton(
                  onPressed: () async {
                    final request = CustomerCreateRequest(
                      fName: 'Test',
                      lName: 'Customer',
                      email: 'test${DateTime.now().millisecondsSinceEpoch}@example.com',
                      phone: '**********',
                      password: 'password123',
                    );
                    await controller.createCustomer(request);
                  },
                  child: const Text('Test Customer Create API'),
                ),
                const SizedBox(height: 20),
                Text('Debug Info:', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 10),
                Text('Is Loading: ${controller.isLoading}'),
                Text('Is Creating: ${controller.isCreating}'),
                Text('Customer Count: ${controller.customers.length}'),
                Text('Total Count: ${controller.totalCount}'),
                const SizedBox(height: 20),
                if (controller.customers.isNotEmpty)
                  Expanded(
                    child: ListView.builder(
                      itemCount: controller.customers.length,
                      itemBuilder: (context, index) {
                        final customer = controller.customers[index];
                        return Card(
                          child: ListTile(
                            title: Text(customer.name),
                            subtitle: Text('${customer.email} - ${customer.phone}'),
                            trailing: Text('ID: ${customer.id}'),
                          ),
                        );
                      },
                    ),
                  )
                else
                  const Text('No customers found'),
              ],
            );
          },
        ),
      ),
    );
  }
}
