import 'package:flutter/material.dart';
import 'package:sixvalley_vendor_app/features/customer_management/models/customer_model.dart';

class CustomerStatusTestWidget extends StatelessWidget {
  const CustomerStatusTestWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Test different status value formats
    final testCases = [
      {'is_active': 1},           // Integer 1
      {'is_active': 0},           // Integer 0
      {'is_active': '1'},         // String '1'
      {'is_active': '0'},         // String '0'
      {'is_active': true},        // Boolean true
      {'is_active': false},       // Boolean false
      {'is_active': 'true'},      // String 'true'
      {'is_active': 'false'},     // String 'false'
      {'is_active': 'active'},    // String 'active'
      {'is_active': 'inactive'},  // String 'inactive'
      {'is_active': null},        // Null value
      {},                         // Missing field
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Status Test'),
      ),
      body: ListView.builder(
        itemCount: testCases.length,
        itemBuilder: (context, index) {
          final testCase = testCases[index];
          
          // Create a test customer with minimal required fields
          final testCustomer = CustomerModel(
            id: index,
            name: 'Test Customer $index',
            email: 'test$<EMAIL>',
            phone: '123456789$index',
            isActive: CustomerModel.parseActiveStatus(testCase['is_active']),
          );

          return Card(
            margin: const EdgeInsets.all(8),
            child: ListTile(
              title: Text('Test Case $index'),
              subtitle: Text('Raw value: ${testCase['is_active']} (${testCase['is_active'].runtimeType})'),
              trailing: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: testCustomer.isCustomerActive 
                          ? Colors.green.withOpacity(0.1) 
                          : Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      testCustomer.isCustomerActive ? 'Active' : 'Inactive',
                      style: TextStyle(
                        color: testCustomer.isCustomerActive ? Colors.green : Colors.red,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Text(
                    'Parsed: ${testCustomer.isActive}',
                    style: const TextStyle(fontSize: 10, color: Colors.grey),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
