import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sixvalley_vendor_app/localization/language_constrants.dart';
import '../provider/customer_provider.dart';
import '../models/customer_model.dart';
import 'add_customer_screen.dart';
import 'customer_details_screen.dart';

class CustomerListScreen extends StatefulWidget {
  const CustomerListScreen({Key? key}) : super(key: key);

  @override
  State<CustomerListScreen> createState() => _CustomerListScreenState();
}

class _CustomerListScreenState extends State<CustomerListScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(context, listen: false).getCustomerList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(getTranslated('customers', context) ?? 'Customers'),
        elevation: 1,
        actions: [
          IconButton(
            onPressed: () {
              Provider.of<CustomerProvider>(context, listen: false).getCustomerList();
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => ChangeNotifierProvider.value(
                value: Provider.of<CustomerProvider>(context, listen: false),
                child: const AddCustomerScreen(),
              ),
            ),
          );
          
          // If customer was added successfully, refresh the list
          if (result == true && mounted) {
            Provider.of<CustomerProvider>(context, listen: false).getCustomerList();
          }
        },
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: Consumer<CustomerProvider>(
        builder: (context, customerProvider, _) {
          return RefreshIndicator(
            onRefresh: () async {
              await customerProvider.getCustomerList();
            },
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: getTranslated('search_by_name_email_phone', context) ?? 'Search by name, email, or phone',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Theme.of(context).dividerColor),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Theme.of(context).dividerColor),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Theme.of(context).primaryColor),
                      ),
                    ),
                    onSubmitted: (value) {
                      customerProvider.getCustomerList(search: value);
                    },
                  ),
                ),
                if (customerProvider.isLoading)
                  const Expanded(
                    child: Center(child: CircularProgressIndicator()),
                  ),
                if (!customerProvider.isLoading)
                  Expanded(
                    child: customerProvider.customers.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.people_outline,
                                  size: 64,
                                  color: Theme.of(context).disabledColor,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  getTranslated('no_customers_found', context) ?? 'No customers found',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Theme.of(context).disabledColor,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: () {
                                    customerProvider.getCustomerList();
                                  },
                                  child: Text(getTranslated('retry', context) ?? 'Retry'),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: customerProvider.customers.length,
                            itemBuilder: (context, index) {
                              CustomerModel customer = customerProvider.customers[index];
                              return Dismissible(
                                key: Key('customer_${customer.id}'),
                                direction: DismissDirection.endToStart,
                                background: Container(
                                  alignment: Alignment.centerRight,
                                  padding: const EdgeInsets.only(right: 20.0),
                                  color: Colors.red,
                                  child: const Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.delete,
                                        color: Colors.white,
                                        size: 24,
                                      ),
                                      SizedBox(height: 4),
                                      Text(
                                        'Delete',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                confirmDismiss: (direction) async {
                                  return await _showDeleteConfirmationDialog(context, customer, customerProvider);
                                },
                                onDismissed: (direction) {
                                  // The actual deletion is handled in confirmDismiss
                                  // This is just for cleanup if needed
                                },
                                child: Card(
                                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                                  child: InkWell(
                                    onTap: () async {
                                      final result = await Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (_) => ChangeNotifierProvider.value(
                                            value: Provider.of<CustomerProvider>(context, listen: false),
                                            child: CustomerDetailsScreen(
                                              customerId: customer.id,
                                              customerName: customer.name,
                                            ),
                                          ),
                                        ),
                                      );
                                      
                                      // If customer was updated or deleted, refresh the list
                                      if (result == true && mounted) {
                                        customerProvider.getCustomerList();
                                      }
                                    },
                                    child: ListTile(
                                      leading: CircleAvatar(
                                        backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                                        child: Text(
                                          customer.name.isNotEmpty ? customer.name[0].toUpperCase() : 'C',
                                          style: TextStyle(
                                            color: Theme.of(context).primaryColor,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      title: Text(
                                        customer.name,
                                        style: const TextStyle(fontWeight: FontWeight.w600),
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          if (customer.email.isNotEmpty)
                                            Row(
                                              children: [
                                                const Icon(Icons.email, size: 14, color: Colors.grey),
                                                const SizedBox(width: 4),
                                                Expanded(child: Text(customer.email)),
                                              ],
                                            ),
                                          if (customer.phone.isNotEmpty)
                                            Row(
                                              children: [
                                                const Icon(Icons.phone, size: 14, color: Colors.grey),
                                                const SizedBox(width: 4),
                                                Text(customer.phone),
                                              ],
                                            ),
                                        ],
                                      ),
                                      trailing: _buildStatusIndicator(customer, context),
                                      isThreeLine: true,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                if (!customerProvider.isLoading && customerProvider.totalCount > customerProvider.customers.length)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(
                      child: ElevatedButton(
                        onPressed: () {
                          customerProvider.getCustomerList(offset: customerProvider.customers.length ~/ customerProvider.limit + 1);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        child: Text(getTranslated('load_more', context) ?? 'Load More'),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusIndicator(CustomerModel customer, BuildContext context) {
    // Determine if customer is active
    bool isActive = _isCustomerActive(customer);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        isActive 
            ? (getTranslated('active', context) ?? 'Active')
            : (getTranslated('inactive', context) ?? 'Inactive'),
        style: TextStyle(
          color: isActive ? Colors.green : Colors.red,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  bool _isCustomerActive(CustomerModel customer) {
    return customer.isCustomerActive;
  }

  Future<bool> _showDeleteConfirmationDialog(BuildContext context, CustomerModel customer, CustomerProvider customerProvider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            getTranslated('delete_customer', context) ?? 'Delete Customer',
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange[600],
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                getTranslated('delete_customer_confirmation', context) ?? 
                'Are you sure you want to delete this customer?',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 8),
              Text(
                'Customer: ${customer.name}',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.red[600],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        getTranslated('delete_customer_warning', context) ?? 
                        'This action cannot be undone.',
                        style: TextStyle(
                          color: Colors.red[700],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                getTranslated('cancel', context) ?? 'Cancel',
                style: TextStyle(color: Theme.of(context).primaryColor),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                final success = await customerProvider.deleteCustomer(customer.id);
                if (context.mounted) {
                  Navigator.of(context).pop(success);
                  if (success) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          getTranslated('customer_deleted_successfully', context) ?? 
                          'Customer deleted successfully',
                        ),
                        backgroundColor: Colors.green,
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          getTranslated('failed_to_delete_customer', context) ?? 
                          'Failed to delete customer',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(getTranslated('delete', context) ?? 'Delete'),
            ),
          ],
        );
      },
    );

    return confirmed ?? false;
  }
}
