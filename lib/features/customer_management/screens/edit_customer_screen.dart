import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:sixvalley_vendor_app/localization/language_constrants.dart';
import 'package:sixvalley_vendor_app/utill/dimensions.dart';
import 'package:sixvalley_vendor_app/utill/styles.dart';
import 'package:sixvalley_vendor_app/common/basewidgets/custom_button_widget.dart';
import 'package:sixvalley_vendor_app/common/basewidgets/textfeild/custom_text_feild_widget.dart';
import 'package:sixvalley_vendor_app/features/auth/widgets/code_picker_widget.dart';
import '../provider/customer_provider.dart';
import '../models/customer_model.dart';

class EditCustomerScreen extends StatefulWidget {
  final CustomerModel customer;

  const EditCustomerScreen({
    Key? key,
    required this.customer,
  }) : super(key: key);

  @override
  State<EditCustomerScreen> createState() => _EditCustomerScreenState();
}

class _EditCustomerScreenState extends State<EditCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _countryCodeController;
  late TextEditingController _countryController;
  late TextEditingController _cityController;
  late TextEditingController _zipController;
  late TextEditingController _streetAddressController;
  
  bool _isActive = true;
  String? _countryDialCode;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _firstNameController = TextEditingController(text: widget.customer.fName ?? '');
    _lastNameController = TextEditingController(text: widget.customer.lName ?? '');
    _emailController = TextEditingController(text: widget.customer.email);
    
    // Parse phone number to extract country code and phone number
    String fullPhone = widget.customer.phone;
    _countryDialCode = widget.customer.countryCode ?? "+91";
    
    // Extract phone number without country code
    String phoneWithoutCode = fullPhone;
    if (fullPhone.startsWith(_countryDialCode!)) {
      phoneWithoutCode = fullPhone.substring(_countryDialCode!.length);
    }
    
    _phoneController = TextEditingController(text: phoneWithoutCode);
    _countryCodeController = TextEditingController(text: _countryDialCode ?? '');
    _countryController = TextEditingController(text: widget.customer.country ?? '');
    _cityController = TextEditingController(text: widget.customer.city ?? '');
    _zipController = TextEditingController(text: widget.customer.zip ?? '');
    _streetAddressController = TextEditingController(text: widget.customer.streetAddress ?? '');
    _isActive = widget.customer.isCustomerActive;
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _countryCodeController.dispose();
    _countryController.dispose();
    _cityController.dispose();
    _zipController.dispose();
    _streetAddressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(getTranslated('edit_customer', context) ?? 'Edit Customer'),
        elevation: 1,
      ),
      body: Consumer<CustomerProvider>(
        builder: (context, customerProvider, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(Dimensions.paddingSizeDefault),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Section
                  Text(
                    getTranslated('personal_information', context) ?? 'Personal Information',
                    style: robotoMedium.copyWith(
                      fontSize: Dimensions.fontSizeLarge,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),
                  
                  // First Name
                  Text(
                    '${getTranslated('first_name', context) ?? 'First Name'} *',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _firstNameController,
                    hintText: getTranslated('enter_first_name', context) ?? 'Enter first name',
                    textInputType: TextInputType.name,
                    isValidator: true,
                    validatorMessage: _validateRequired(_firstNameController.text, 'First name'),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),
                  
                  // Last Name
                  Text(
                    '${getTranslated('last_name', context) ?? 'Last Name'} *',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _lastNameController,
                    hintText: getTranslated('enter_last_name', context) ?? 'Enter last name',
                    textInputType: TextInputType.name,
                    isValidator: true,
                    validatorMessage: _validateRequired(_lastNameController.text, 'Last name'),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),
                  
                  // Email
                  Text(
                    '${getTranslated('email', context) ?? 'Email'} *',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _emailController,
                    hintText: getTranslated('enter_email', context) ?? 'Enter email',
                    textInputType: TextInputType.emailAddress,
                    isValidator: true,
                    validatorMessage: _validateEmail(_emailController.text),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),

                  // Phone Number with Country Code
                  Padding(
                    padding: const EdgeInsets.only(left: Dimensions.paddingSizeSmall, right: Dimensions.paddingSizeSmall),
                    child: Text(
                      '${getTranslated('phone', context) ?? 'Phone'} *',
                      style: robotoRegular.copyWith(fontSize: Dimensions.fontSizeDefault),
                    ),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(width: 1, color: Theme.of(context).hintColor.withValues(alpha: 0.35)),
                      color: Theme.of(context).highlightColor,
                      borderRadius: BorderRadius.circular(Dimensions.paddingSizeExtraSmall),
                    ),
                    margin: const EdgeInsets.only(left: Dimensions.paddingSizeSmall, right: Dimensions.paddingSizeSmall),
                    child: Row(children: [
                      CodePickerWidget(
                        onChanged: (CountryCode countryCode) {
                          _countryDialCode = countryCode.dialCode;
                          _countryCodeController.text = countryCode.dialCode ?? '';
                        },
                        initialSelection: _countryDialCode,
                        favorite: [_countryDialCode!],
                        showDropDownButton: true,
                        padding: EdgeInsets.zero,
                        showFlagMain: true,
                        textStyle: TextStyle(color: Theme.of(context).textTheme.displayLarge!.color),
                        dialogTextStyle: robotoRegular.copyWith(
                          fontSize: Dimensions.fontSizeDefault,
                          color: Theme.of(context).textTheme.bodyLarge!.color,
                        ),
                      ),
                      Expanded(
                        child: CustomTextFieldWidget(
                          controller: _phoneController,
                          hintText: getTranslated('mobile_hint', context) ?? 'Enter phone',
                          textInputType: TextInputType.phone,
                          isPhoneNumber: true,
                          border: false,
                          focusBorder: false,
                          textInputAction: TextInputAction.next,
                        ),
                      ),
                    ]),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeMedium),
                  
                  // Status Toggle
                  Row(
                    children: [
                      Text(
                        getTranslated('customer_status', context) ?? 'Customer Status:',
                        style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                      ),
                      const SizedBox(width: 16),
                      Switch(
                        value: _isActive,
                        onChanged: (value) {
                          setState(() {
                            _isActive = value;
                          });
                        },
                        activeColor: Theme.of(context).primaryColor,
                      ),
                      Text(
                        _isActive 
                            ? (getTranslated('active', context) ?? 'Active')
                            : (getTranslated('inactive', context) ?? 'Inactive'),
                        style: TextStyle(
                          color: _isActive ? Colors.green : Colors.red,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: Dimensions.paddingSizeLarge),
                  
                  // Address Section
                  Text(
                    getTranslated('address_information', context) ?? 'Address Information',
                    style: robotoMedium.copyWith(
                      fontSize: Dimensions.fontSizeLarge,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),
                  
                  // Country
                  Text(
                    getTranslated('country', context) ?? 'Country',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _countryController,
                    hintText: getTranslated('enter_country', context) ?? 'Enter country',
                    textInputType: TextInputType.text,
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),
                  
                  // City
                  Text(
                    getTranslated('city', context) ?? 'City',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _cityController,
                    hintText: getTranslated('enter_city', context) ?? 'Enter city',
                    textInputType: TextInputType.text,
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),
                  
                  // ZIP Code
                  Text(
                    getTranslated('zip_code', context) ?? 'ZIP Code',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _zipController,
                    hintText: getTranslated('enter_zip', context) ?? 'Enter ZIP code',
                    textInputType: TextInputType.text,
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),
                  
                  // Street Address
                  Text(
                    getTranslated('street_address', context) ?? 'Street Address',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _streetAddressController,
                    hintText: getTranslated('enter_street_address', context) ?? 'Enter street address',
                    textInputType: TextInputType.multiline,
                    maxLine: 2,
                  ),
                  
                  const SizedBox(height: Dimensions.paddingSizeLarge),
                  
                  // Update Button
                  CustomButtonWidget(
                    isLoading: customerProvider.isUpdating,
                    btnTxt: getTranslated('update_customer', context) ?? 'Update Customer',
                    onTap: customerProvider.isUpdating ? null : _updateCustomer,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _updateCustomer() async {
    if (!_validateForm()) {
      return;
    }

    final customerProvider = Provider.of<CustomerProvider>(context, listen: false);
    
    final updateRequest = CustomerUpdateRequest(
      fName: _firstNameController.text.trim(),
      lName: _lastNameController.text.trim(),
      email: _emailController.text.trim(),
      phone: "${_countryDialCode}${_phoneController.text.trim()}",
      countryCode: _countryDialCode,
      isActive: _isActive,
      country: _countryController.text.trim().isNotEmpty 
          ? _countryController.text.trim() 
          : null,
      city: _cityController.text.trim().isNotEmpty 
          ? _cityController.text.trim() 
          : null,
      zip: _zipController.text.trim().isNotEmpty 
          ? _zipController.text.trim() 
          : null,
      streetAddress: _streetAddressController.text.trim().isNotEmpty 
          ? _streetAddressController.text.trim() 
          : null,
    );

    final success = await customerProvider.updateCustomer(widget.customer.id, updateRequest);
    
    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(getTranslated('customer_updated_successfully', context) ?? 'Customer updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // Return true to indicate success
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(getTranslated('failed_to_update_customer', context) ?? 'Failed to update customer'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String? _validateRequired(String value, String fieldName) {
    if (value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  String? _validateEmail(String value) {
    if (value.trim().isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  bool _validateForm() {
    return _validateRequired(_firstNameController.text, 'First name') == null &&
           _validateRequired(_lastNameController.text, 'Last name') == null &&
           _validateEmail(_emailController.text) == null &&
           _validateRequired(_phoneController.text, 'Phone') == null;
  }
}
