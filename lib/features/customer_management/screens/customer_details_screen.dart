import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sixvalley_vendor_app/localization/language_constrants.dart';
import '../provider/customer_provider.dart';
import '../models/customer_model.dart';
import 'edit_customer_screen.dart';

class CustomerDetailsScreen extends StatefulWidget {
  final int customerId;
  final String customerName;

  const CustomerDetailsScreen({
    Key? key,
    required this.customerId,
    required this.customerName,
  }) : super(key: key);

  @override
  State<CustomerDetailsScreen> createState() => _CustomerDetailsScreenState();
}

class _CustomerDetailsScreenState extends State<CustomerDetailsScreen> {
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(context, listen: false)
          .getCustomerDetails(widget.customerId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, _) {
        return WillPopScope(
          onWillPop: () async {
            Navigator.of(context).pop(_hasChanges);
            return false;
          },
          child: Scaffold(
          appBar: AppBar(
            title: Text(widget.customerName),
            elevation: 1,
            actions: [
              if (customerProvider.customerDetails != null) ...[
                IconButton(
                  onPressed: () async {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => ChangeNotifierProvider.value(
                          value: Provider.of<CustomerProvider>(context, listen: false),
                          child: EditCustomerScreen(
                            customer: customerProvider.customerDetails!,
                          ),
                        ),
                      ),
                    );
                    
                    // If customer was updated successfully, refresh the details
                    if (result == true && mounted) {
                      _hasChanges = true;
                      Provider.of<CustomerProvider>(context, listen: false)
                          .getCustomerDetails(widget.customerId);
                    }
                  },
                  icon: const Icon(Icons.edit),
                  tooltip: 'Edit Customer',
                ),
                PopupMenuButton<String>(
                  onSelected: (value) async {
                    if (value == 'delete') {
                      await _showDeleteConfirmationDialog(context, customerProvider);
                    }
                  },
                  itemBuilder: (BuildContext context) => [
                    PopupMenuItem<String>(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red[600]),
                          const SizedBox(width: 8),
                          Text(
                            'Delete Customer',
                            style: TextStyle(color: Colors.red[600]),
                          ),
                        ],
                      ),
                    ),
                  ],
                  icon: const Icon(Icons.more_vert),
                ),
              ],
            ],
          ),
          body: _buildBody(context, customerProvider),
        ),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, CustomerProvider customerProvider) {
    if (customerProvider.isLoadingDetails) {
      return const Center(child: CircularProgressIndicator());
    }

    final customerDetails = customerProvider.customerDetails;
    if (customerDetails == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).disabledColor,
            ),
            const SizedBox(height: 16),
            Text(
              getTranslated('customer_details_not_found', context) ?? 'Customer details not found',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).disabledColor,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                customerProvider.getCustomerDetails(widget.customerId);
              },
              child: Text(getTranslated('retry', context) ?? 'Retry'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await customerProvider.getCustomerDetails(widget.customerId);
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Customer Info Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                          child: Text(
                            customerDetails.name.isNotEmpty 
                                ? customerDetails.name[0].toUpperCase() 
                                : 'C',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                customerDetails.name,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              _buildStatusBadge(customerDetails, context),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildInfoRow(
                      Icons.email,
                      getTranslated('email', context) ?? 'Email',
                      customerDetails.email,
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow(
                      Icons.phone,
                      getTranslated('phone', context) ?? 'Phone',
                      customerDetails.phone,
                    ),
                    if (customerDetails.countryCode != null) ...[
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        Icons.flag,
                        getTranslated('country_code', context) ?? 'Country Code',
                        customerDetails.countryCode!,
                      ),
                    ],
                    if (customerDetails.referralCode != null) ...[
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        Icons.card_giftcard,
                        getTranslated('referral_code', context) ?? 'Referral Code',
                        customerDetails.referralCode!,
                      ),
                    ],
                    const SizedBox(height: 8),
                    _buildInfoRow(
                      Icons.calendar_today,
                      getTranslated('joined_date', context) ?? 'Joined Date',
                      _formatDate(customerDetails.createdAt),
                    ),
                    if (customerDetails.country != null || customerDetails.city != null || customerDetails.streetAddress != null) ...[
                      const SizedBox(height: 16),
                      Text(
                        getTranslated('address_information', context) ?? 'Address Information',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (customerDetails.country != null)
                        _buildInfoRow(
                          Icons.location_on,
                          getTranslated('country', context) ?? 'Country',
                          customerDetails.country!,
                        ),
                      if (customerDetails.city != null) ...[
                        const SizedBox(height: 8),
                        _buildInfoRow(
                          Icons.location_city,
                          getTranslated('city', context) ?? 'City',
                          customerDetails.city!,
                        ),
                      ],
                      if (customerDetails.zip != null) ...[
                        const SizedBox(height: 8),
                        _buildInfoRow(
                          Icons.local_post_office,
                          getTranslated('zip_code', context) ?? 'ZIP Code',
                          customerDetails.zip!,
                        ),
                      ],
                      if (customerDetails.streetAddress != null) ...[
                        const SizedBox(height: 8),
                        _buildInfoRow(
                          Icons.home,
                          getTranslated('street_address', context) ?? 'Street Address',
                          customerDetails.streetAddress!,
                        ),
                      ],
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Orders Summary Card
            // if (customerDetails.ordersCount != null && customerDetails.ordersCount! > 0)
            //   Card(
            //     child: Padding(
            //       padding: const EdgeInsets.all(16),
            //       child: Column(
            //         crossAxisAlignment: CrossAxisAlignment.start,
            //         children: [
            //           Text(
            //             getTranslated('orders_summary', context) ?? 'Orders Summary',
            //             style: const TextStyle(
            //               fontSize: 18,
            //               fontWeight: FontWeight.bold,
            //             ),
            //           ),
            //           const SizedBox(height: 12),
            //           Row(
            //             children: [
            //               Icon(
            //                 Icons.shopping_bag,
            //                 color: Theme.of(context).primaryColor,
            //               ),
            //               const SizedBox(width: 8),
            //               Text(
            //                 '${customerDetails.ordersCount} ${getTranslated('total_orders', context) ?? 'Total Orders'}',
            //                 style: const TextStyle(fontSize: 16),
            //               ),
            //             ],
            //           ),
            //         ],
            //       ),
            //     ),
            //   ),
            
            // // Recent Orders Section (if orders data is available)
            // if (customerDetails.orders != null && customerDetails.orders!.isNotEmpty) ...[
            //   const SizedBox(height: 16),
            //   Card(
            //     child: Padding(
            //       padding: const EdgeInsets.all(16),
            //       child: Column(
            //         crossAxisAlignment: CrossAxisAlignment.start,
            //         children: [
            //           Text(
            //             getTranslated('recent_orders', context) ?? 'Recent Orders',
            //             style: const TextStyle(
            //               fontSize: 18,
            //               fontWeight: FontWeight.bold,
            //             ),
            //           ),
            //           const SizedBox(height: 12),
            //           // Here you can add order list items if needed
            //           Text(
            //             '${customerDetails.orders!.length} ${getTranslated('recent_orders_found', context) ?? 'recent orders found'}',
            //             style: TextStyle(
            //               color: Theme.of(context).textTheme.bodyMedium?.color,
            //             ),
            //           ),
            //         ],
            //       ),
            //     ),
            //   ),
            // ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(CustomerModel customer, BuildContext context) {
    bool isActive = customer.isCustomerActive;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        isActive 
            ? (getTranslated('active', context) ?? 'Active')
            : (getTranslated('inactive', context) ?? 'Inactive'),
        style: TextStyle(
          color: isActive ? Colors.green : Colors.red,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
        ),
        Expanded(
          child: Text(
            value.isNotEmpty ? value : 'N/A',
            style: const TextStyle(fontWeight: FontWeight.w400),
          ),
        ),
      ],
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return 'N/A';
    try {
      DateTime date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  Future<void> _showDeleteConfirmationDialog(BuildContext context, CustomerProvider customerProvider) async {
    final customerName = customerProvider.customerDetails?.name ?? 'this customer';
    
    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            getTranslated('delete_customer', context) ?? 'Delete Customer',
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange[600],
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                getTranslated('delete_customer_confirmation', context) ?? 
                'Are you sure you want to delete this customer?',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 8),
              Text(
                'Customer: $customerName',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.red[600],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        getTranslated('delete_customer_warning', context) ?? 
                        'This action cannot be undone. All customer data will be permanently removed.',
                        style: TextStyle(
                          color: Colors.red[700],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                getTranslated('cancel', context) ?? 'Cancel',
                style: TextStyle(color: Theme.of(context).primaryColor),
              ),
            ),
            Consumer<CustomerProvider>(
              builder: (context, controller, _) {
                return ElevatedButton(
                  onPressed: controller.isDeleting 
                      ? null 
                      : () async {
                          final success = await controller.deleteCustomer(widget.customerId);
                          if (mounted) {
                            Navigator.of(context).pop(success);
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: controller.isDeleting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(getTranslated('delete', context) ?? 'Delete'),
                );
              },
            ),
          ],
        );
      },
    );

    if (confirmed == true && mounted) {
      _hasChanges = true;
      // Show success message and navigate back
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            getTranslated('customer_deleted_successfully', context) ?? 
            'Customer deleted successfully',
          ),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop(true); // Return to customer list
    } else if (confirmed == false && mounted) {
      // Show error message if deletion failed
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            getTranslated('failed_to_delete_customer', context) ?? 
            'Failed to delete customer',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
