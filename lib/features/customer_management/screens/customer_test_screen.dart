import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sixvalley_vendor_app/features/customer_management/provider/customer_provider.dart';

class CustomerTestScreen extends StatelessWidget {
  const CustomerTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer API Test'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () async {
                final controller = Provider.of<CustomerProvider>(context, listen: false);
                await controller.getCustomerList();
                print('Customer count: ${controller.customers.length}');
                print('Total count: ${controller.totalCount}');
                print('Is loading: ${controller.isLoading}');
              },
              child: const Text('Test Customer API'),
            ),
            const SizedBox(height: 20),
            Consumer<CustomerProvider>(
              builder: (context, controller, _) {
                return Column(
                  children: [
                    Text('Customer count: ${controller.customers.length}'),
                    Text('Total count: ${controller.totalCount}'),
                    Text('Is loading: ${controller.isLoading}'),
                    const SizedBox(height: 20),
                    if (controller.customers.isNotEmpty)
                      Expanded(
                        child: ListView.builder(
                          itemCount: controller.customers.length,
                          itemBuilder: (context, index) {
                            final customer = controller.customers[index];
                            return ListTile(
                              title: Text(customer.name),
                              subtitle: Text('${customer.email} - ${customer.phone}'),
                            );
                          },
                        ),
                      ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
