import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:sixvalley_vendor_app/localization/language_constrants.dart';
import 'package:sixvalley_vendor_app/utill/dimensions.dart';
import 'package:sixvalley_vendor_app/utill/styles.dart';
import 'package:sixvalley_vendor_app/utill/app_constants.dart';
import 'package:sixvalley_vendor_app/common/basewidgets/custom_button_widget.dart';
import 'package:sixvalley_vendor_app/common/basewidgets/textfeild/custom_text_feild_widget.dart';
import 'package:sixvalley_vendor_app/features/auth/widgets/code_picker_widget.dart';
import '../provider/customer_provider.dart';
import '../models/customer_model.dart';

class AddCustomerScreen extends StatefulWidget {
  const AddCustomerScreen({Key? key}) : super(key: key);

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _fNameController = TextEditingController();
  final TextEditingController _lNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _countryCodeController = TextEditingController();
  final TextEditingController _referralCodeController = TextEditingController();
  
  String? _countryDialCode = "+91";

  @override
  void dispose() {
    _fNameController.dispose();
    _lNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _countryCodeController.dispose();
    _referralCodeController.dispose();
    super.dispose();
  }

  String? _validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone is required';
    }
    String numericValue = value.replaceAll(RegExp(r'[^0-9]'), '');
    if (numericValue.length < 4 || numericValue.length > 20) {
      return 'Phone must be 4-20 digits';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(getTranslated('add_customer', context) ?? 'Add Customer'),
        elevation: 1,
      ),
      body: Consumer<CustomerProvider>(
        builder: (context, CustomerProvider, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(Dimensions.paddingSizeDefault),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    getTranslated('customer_information', context) ?? 'Customer Information',
                    style: robotoMedium.copyWith(
                      fontSize: Dimensions.fontSizeLarge,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),

                  // First Name
                  Text(
                    '${getTranslated('first_name', context) ?? 'First Name'} *',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _fNameController,
                    hintText: getTranslated('enter_first_name', context) ?? 'Enter first name',
                    textInputType: TextInputType.name,
                    isValidator: true,
                    validatorMessage: _validateRequired(_fNameController.text, 'First name'),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),

                  // Last Name
                  Text(
                    '${getTranslated('last_name', context) ?? 'Last Name'} *',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _lNameController,
                    hintText: getTranslated('enter_last_name', context) ?? 'Enter last name',
                    textInputType: TextInputType.name,
                    isValidator: true,
                    validatorMessage: _validateRequired(_lNameController.text, 'Last name'),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),

                  // Email
                  Text(
                    '${getTranslated('email', context) ?? 'Email'} *',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _emailController,
                    hintText: getTranslated('enter_email', context) ?? 'Enter email address',
                    textInputType: TextInputType.emailAddress,
                    isValidator: true,
                    validatorMessage: _validateEmail(_emailController.text),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),

                  // Phone Number with Country Code
                  Padding(
                    padding: const EdgeInsets.only(left: Dimensions.paddingSizeSmall, right: Dimensions.paddingSizeSmall),
                    child: Text(
                      '${getTranslated('phone', context) ?? 'Phone'} *',
                      style: robotoRegular.copyWith(fontSize: Dimensions.fontSizeDefault),
                    ),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(width: 1, color: Theme.of(context).hintColor.withValues(alpha: 0.35)),
                      color: Theme.of(context).highlightColor,
                      borderRadius: BorderRadius.circular(Dimensions.paddingSizeExtraSmall),
                    ),
                    margin: const EdgeInsets.only(left: Dimensions.paddingSizeSmall, right: Dimensions.paddingSizeSmall),
                    child: Row(children: [
                      CodePickerWidget(
                        onChanged: (CountryCode countryCode) {
                          _countryDialCode = countryCode.dialCode;
                          _countryCodeController.text = countryCode.dialCode ?? '';
                        },
                        initialSelection: _countryDialCode,
                        favorite: [_countryDialCode!],
                        showDropDownButton: true,
                        padding: EdgeInsets.zero,
                        showFlagMain: true,
                        textStyle: TextStyle(color: Theme.of(context).textTheme.displayLarge!.color),
                        dialogTextStyle: robotoRegular.copyWith(
                          fontSize: Dimensions.fontSizeDefault,
                          color: Theme.of(context).textTheme.bodyLarge!.color,
                        ),
                      ),
                      Expanded(
                        child: CustomTextFieldWidget(
                          controller: _phoneController,
                          hintText: getTranslated('mobile_hint', context) ?? 'Enter phone number',
                          textInputType: TextInputType.phone,
                          isPhoneNumber: true,
                          border: false,
                          focusBorder: false,
                          textInputAction: TextInputAction.next,
                        ),
                      ),
                    ]),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeMedium),

                  // Password
                  Text(
                    '${getTranslated('password', context) ?? 'Password'} *',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _passwordController,
                    hintText: getTranslated('enter_password', context) ?? 'Enter password',
                    textInputType: TextInputType.visiblePassword,
                    isPassword: true,
                    isValidator: true,
                    validatorMessage: _validatePassword(_passwordController.text),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeDefault),

                  // Referral Code
                  Text(
                    getTranslated('referral_code', context) ?? 'Referral Code',
                    style: robotoMedium.copyWith(fontSize: Dimensions.fontSizeDefault),
                  ),
                  const SizedBox(height: Dimensions.paddingSizeSmall),
                  CustomTextFieldWidget(
                    controller: _referralCodeController,
                    hintText: getTranslated('enter_referral_code', context) ?? 'Enter referral code (optional)',
                    textInputType: TextInputType.text,
                  ),
                  const SizedBox(height: Dimensions.paddingSizeLarge),

                  // Submit Button
                  CustomButtonWidget(
                    isLoading: CustomerProvider.isCreating,
                    btnTxt: getTranslated('add_customer', context) ?? 'Add Customer',
                    onTap: () async {
                      // Validate form first
                      bool isFormValid = _validateForm();
                      
                      if (isFormValid) {
                        CustomerCreateRequest request = CustomerCreateRequest(
                          fName: _fNameController.text.trim(),
                          lName: _lNameController.text.trim(),
                          email: _emailController.text.trim(),
                          phone: "${_countryDialCode}${_phoneController.text.trim()}",
                          password: _passwordController.text.trim(),
                          countryCode: _countryDialCode,
                          referralCode: _referralCodeController.text.trim().isNotEmpty 
                              ? _referralCodeController.text.trim() 
                              : null,
                        );

                        bool success = await CustomerProvider.createCustomer(request);
                        if (mounted) {
                          if (success) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(getTranslated('customer_added_successfully', context) ?? 'Customer added successfully!'),
                                backgroundColor: Colors.green,
                              ),
                            );
                            Navigator.pop(context, true); // Return true to indicate success
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(getTranslated('customer_add_failed', context) ?? 'Failed to add customer. Please try again.'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      } else {
                        // Show validation errors
                        String? firstNameError = _validateRequired(_fNameController.text, 'First name');
                        String? lastNameError = _validateRequired(_lNameController.text, 'Last name');
                        String? emailError = _validateEmail(_emailController.text);
                        String? phoneError = _validatePhone(_phoneController.text);
                        String? passwordError = _validatePassword(_passwordController.text);
                        
                        String errorMessage = '';
                        if (firstNameError != null) errorMessage = firstNameError;
                        else if (lastNameError != null) errorMessage = lastNameError;
                        else if (emailError != null) errorMessage = emailError;
                        else if (phoneError != null) errorMessage = phoneError;
                        else if (passwordError != null) errorMessage = passwordError;
                        
                        if (errorMessage.isNotEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(errorMessage),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  bool _validateForm() {
    return _validateRequired(_fNameController.text, 'First name') == null &&
           _validateRequired(_lNameController.text, 'Last name') == null &&
           _validateEmail(_emailController.text) == null &&
           _validatePhone(_phoneController.text) == null &&
           _validatePassword(_passwordController.text) == null;
  }
}
