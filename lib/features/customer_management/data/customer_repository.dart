import 'package:sixvalley_vendor_app/data/datasource/remote/dio/dio_client.dart';
import 'package:sixvalley_vendor_app/data/datasource/remote/exception/api_error_handler.dart';
import 'package:sixvalley_vendor_app/data/model/response/base/api_response.dart';
import 'package:sixvalley_vendor_app/features/customer_management/domain/repositories/customer_repository_interface.dart';
import 'package:sixvalley_vendor_app/features/customer_management/models/customer_model.dart';
import 'package:sixvalley_vendor_app/utill/app_constants.dart';

class CustomerRepository implements CustomerRepositoryInterface {
  final DioClient dioClient;
  CustomerRepository({required this.dioClient});

  @override
  Future<ApiResponse> getCustomerList({String? search, int limit = 15, int offset = 1}) async {
    try {
      Map<String, dynamic> queryParameters = {
        'limit': limit.toString(),
        'offset': offset.toString(),
      };
      if (search != null && search.isNotEmpty) {
        queryParameters['search'] = search;
      }
      
      print('Customer API URL: ${AppConstants.baseUrl}${AppConstants.customerListUri}');
      print('Customer API Query Parameters: $queryParameters');
      
      final response = await dioClient.get(
        AppConstants.customerListUri,
        queryParameters: queryParameters,
      );
      
      print('Customer API Response Status: ${response.statusCode}');
      print('Customer API Response Data: ${response.data}');
      
      return ApiResponse.withSuccess(response);
    } catch (e) {
      print('Customer API Error: $e');
      return ApiResponse.withError(ApiErrorHandler.getMessage(e));
    }
  }

  @override
  Future<ApiResponse> createCustomer(CustomerCreateRequest request) async {
    try {
      print('Creating customer with data: ${request.toJson()}');
      print('Customer Store URL: ${AppConstants.baseUrl}${AppConstants.customerStoreUri}');
      
      final response = await dioClient.post(
        AppConstants.customerStoreUri,
        data: request.toJson(),
      );
      
      print('Customer Create Response Status: ${response.statusCode}');
      print('Customer Create Response Data: ${response.data}');
      
      return ApiResponse.withSuccess(response);
    } catch (e) {
      print('Customer Create Error: $e');
      return ApiResponse.withError(ApiErrorHandler.getMessage(e));
    }
  }

  @override
  Future<ApiResponse> getCustomerDetails(int customerId) async {
    try {
      print('Customer Details API URL: ${AppConstants.baseUrl}${AppConstants.customerDetailsUri}/$customerId');
      
      final response = await dioClient.get(
        '${AppConstants.customerDetailsUri}/$customerId',
      );
      
      print('Customer Details Response Status: ${response.statusCode}');
      print('Customer Details Response Data: ${response.data}');
      
      return ApiResponse.withSuccess(response);
    } catch (e) {
      print('Customer Details Error: $e');
      return ApiResponse.withError(ApiErrorHandler.getMessage(e));
    }
  }

  @override
  Future<ApiResponse> updateCustomer(int customerId, CustomerUpdateRequest request) async {
    try {
      print('Updating customer $customerId with data: ${request.toJson()}');
      print('Customer Update URL: ${AppConstants.baseUrl}${AppConstants.customerUpdateUri}/$customerId');
      
      final response = await dioClient.put(
        '${AppConstants.customerUpdateUri}/$customerId',
        data: request.toJson(),
      );
      
      print('Customer Update Response Status: ${response.statusCode}');
      print('Customer Update Response Data: ${response.data}');
      
      return ApiResponse.withSuccess(response);
    } catch (e) {
      print('Customer Update Error: $e');
      return ApiResponse.withError(ApiErrorHandler.getMessage(e));
    }
  }

  @override
  Future<ApiResponse> deleteCustomer(int customerId) async {
    try {
      print('Deleting customer $customerId');
      print('Customer Delete URL: ${AppConstants.baseUrl}${AppConstants.customerDeleteUri}/$customerId');
      
      final response = await dioClient.delete(
        '${AppConstants.customerDeleteUri}/$customerId',
      );
      
      print('Customer Delete Response Status: ${response.statusCode}');
      print('Customer Delete Response Data: ${response.data}');
      
      return ApiResponse.withSuccess(response);
    } catch (e) {
      print('Customer Delete Error: $e');
      return ApiResponse.withError(ApiErrorHandler.getMessage(e));
    }
  }
}
