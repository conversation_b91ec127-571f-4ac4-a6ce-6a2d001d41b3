class Dimensions {
  static const double fontSizeExtraSmall = 10.0;
  static const double fontSizeSmall = 12.0;
  static const double fontSizeDefault = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeExtraLarge = 16.0;
  static const double fontSizeExtraLargeTwenty = 20.0;
  static const double fontSizeOverlarge = 24.0;
  static const double fontSizeMaxLarge = 24.0;
  static const double fontSizeWallet = 26.0;
  static const double fontSizeWithdrawableAmount = 30.0;
  static const double fontSizeHeaderLarge = 48.0;


  static const double radiusSmall = 5.0;
  static const double paddingSizeVeryTiny = 2.0;
  static const double paddingSizeOrder = 4.0;
  static const double paddingSizeExtraSmall = 5.0;
  static const double paddingEye = 8.0;
  static const double paddingSeven = 7.0;
  static const double paddingSizeSmall = 10.0;
  static const double paddingSize = 12.0;

  static const double paddingSizeMedium = 15.0;
  static const double paddingSizeDefault = 20.0;
  static const double paddingSizeLarge = 20.0;
  static const double paddingSizeExtraLarge = 25.0;
  static const double paddingSizeButton = 40.0;
  static const double paddingSizeBottomSpace = 80.0;
  static const double bottomSpace = 160.0;



  static const double iconSizeDefault = 18.0;
  static const double iconSizeExtraSmall = 10.0;
  static const double iconSizeSmall = 14.0;
  static const double iconSizeMedium = 20.0;
  static const double iconSizeLarge = 25.0;
  static const double iconSizeExtraLarge = 30.0;
  static const double logoHeight = 40.0;
  static const double incrementButton = 35.0;

  static const double stockOutImageSize = 80.0;
  static const double imageSize = 70.0;
  static const double profileCardHeight = 70.0;
  static const double chatImage = 60.0;
  static const double productImageSize = 50.0;
  static const double productImageSizeItem = 60.0;
  static const double heightWidth50 = 50.0;
  static const double paddingSizeBorder = 3.0;
  static const double paddingSizeCustomerBottom = 35.0;
  static const double paddingSizeMediumBorder = 5.0;
  static const double paddingSizeRevenueBottom = 45.0;
  static const double navbarIconSize = 20.0;
  static const double navbarFontSize = 10.0;
  static const double deliveryManIconSize = 50.0;
  static const double barWidthFlowChart = 2.0;
  static const double topSpace = 30.0;
  static const double radiusExtraLarge = 20.0;
  static const double radiusDefault = 10.0;

  static const double uploadFile = 110.0;
}
