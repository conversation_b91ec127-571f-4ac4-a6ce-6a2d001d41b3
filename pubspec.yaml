name: sixvalley_vendor_app
description: A new Flutter application.
publish_to: 'none'
version: 1.0.0+0

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.6
  get_it: ^7.6.4
  provider: ^6.1.1
  connectivity_plus: ^5.0.2
  dio: ^5.4.0
  intl: ^0.19.0
  shared_preferences: ^2.5.1
  shimmer: ^3.0.0
  image_picker: ^1.0.5
  flutter_switch: ^0.3.2
  cached_network_image: ^3.3.1
  flutter_typeahead: ^5.2.0
  autocomplete_textfield: ^2.0.1
  dotted_border: ^2.1.0
  flutter_html: ^3.0.0-alpha.2
  firebase_core: 3.11.0
  firebase_messaging: 15.2.2
  flutter_local_notifications: ^17.2.2
  path_provider: ^2.1.1
  country_code_picker: ^3.2.0
  pin_code_fields: ^8.0.1
  permission_handler: ^11.1.0
  barcode_widget: ^2.0.4
  file_picker: 8.1.4
  url_launcher: ^6.2.2
  flutter_spinkit: ^5.2.0
  percent_indicator: ^4.2.3
  flutter_speed_dial: ^7.0.0
  barcode_scan2: ^4.3.3
  print_bluetooth_thermal: ^1.1.4
  screenshot: ^3.0.0
  esc_pos_utils: ^1.1.0
  flutter_slidable: ^4.0.0
  readmore: ^2.2.0
  flutter_html_table: ^3.0.0-beta.2
  google_maps_flutter: ^2.5.0
  geocoding: ^2.1.1
  geolocator: ^10.1.0
  syncfusion_flutter_datepicker: ^26.2.8
  textfield_tags: 2.0.2
  http: ^1.1.2
  path: ^1.8.2
  typed_data: ^1.3.2
  animated_floating_buttons: ^0.0.2
  emoji_picker_flutter: ^3.1.0
  syncfusion_flutter_charts: ^27.1.57
  carousel_slider: ^5.0.0
  flutter_downloader: 1.11.7
  open_file: ^3.5.10
  open_file_manager: ^1.0.2
  just_the_tooltip: ^0.0.12
  syncfusion_flutter_pdfviewer: ^29.1.35
  audioplayers: ^6.0.0
  flutter_xlider: ^3.5.0
  video_player: ^2.9.2
  chewie: ^1.8.5
  get_thumbnail_video: ^0.7.3
  flutter_svg: ^2.0.17
  collection: any
  fluttertoast: ^8.2.12

dependency_overrides:
  syncfusion_flutter_core: any


dev_dependencies:
  icons_launcher:
  flutter_lints: ^2.0.1
  flutter_test:
    sdk: flutter


icons_launcher:
  image_path: "assets/image/ic_launcher.png"
  platforms:
    android:
      enable: true
    ios:
      enable: true
    macos:
      enable: true

flutter_icons:
  ios: true
  android: true
  image_path: "assets/image/ic_launcher.png"





flutter:
  uses-material-design: true

  assets:
    - assets/image/
    - assets/language/
    - assets/svg/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/font/roboto/Roboto-Regular.ttf
          weight: 400
        - asset: assets/font/roboto/Roboto-Medium.ttf
          weight: 500
        - asset: assets/font/roboto/Roboto-Bold.ttf
          weight: 700